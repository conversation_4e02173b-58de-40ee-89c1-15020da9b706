---
import '../styles/global.css';
import '../styles/ghibli-effects-2025.css';
import '../styles/performance-optimizations.css';
import '../styles/responsive-enhancements.css';
import { siteConfig } from '../config/site';
import Footer from '../components/Footer.astro';

interface Props {
  title?: string;
  description?: string;
  image?: string;
  containerWidth?: string;
}

const { 
  title = siteConfig.name,
  description = siteConfig.description,
  image = '/images/default-og.png',
  containerWidth = siteConfig.containerWidth 
} = Astro.props;

const canonicalURL = new URL(Astro.url.pathname, Astro.site);
---

<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>{title || siteConfig.name}</title>
    <meta name="description" content={description || siteConfig.description} />
    <link rel="canonical" href={canonicalURL} />
    
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website" />
    <meta property="og:url" content={canonicalURL} />
    <meta property="og:title" content={title} />
    <meta property="og:description" content={description} />
    <meta property="og:image" content={new URL(image, Astro.site)} />
    
    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image" />
    <meta property="twitter:url" content={canonicalURL} />
    <meta property="twitter:title" content={title} />
    <meta property="twitter:description" content={description} />
    <meta property="twitter:image" content={new URL(image, Astro.site)} />
    
    <!-- Enhanced Fonts for 2025 Ghibli Experience -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=Playfair+Display:wght@400;500;600;700;800;900&family=Comfortaa:wght@300;400;500;600;700&family=Nunito:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">

    <!-- Ghibli-inspired CSS Variables and Essential Styles -->
    <style>
      :root {
        --font-ghibli-primary: 'Comfortaa', 'Inter', sans-serif;
        --font-ghibli-heading: 'Playfair Display', 'Comfortaa', serif;
        --font-ghibli-body: 'Nunito', 'Inter', sans-serif;
        --font-ghibli-accent: 'Inter', sans-serif;
      }

      /* Essential animation classes */
      [data-ghibli-animate] {
        opacity: 0;
        transform: translateY(20px);
        transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
      }

      [data-ghibli-animate].animate-in {
        opacity: 1;
        transform: translateY(0);
      }

      .ghibli-glow-enhanced {
        transition: all 0.3s ease;
      }

      .ghibli-glow-enhanced:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
      }

      /* Reduced motion support */
      @media (prefers-reduced-motion: reduce) {
        * {
          animation-duration: 0.01ms !important;
          animation-iteration-count: 1 !important;
          transition-duration: 0.01ms !important;
        }
      }
    </style>
    
    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
  </head>
  <body class="flex flex-col min-h-screen bg-sky-50 font-sans ghibli-dynamic-bg ghibli-particles-enhanced" style="font-family: var(--font-ghibli-body);">
    <header class="bg-sky-100 sticky top-0 z-50 shadow-md transition-all duration-300 backdrop-blur-sm bg-opacity-80 border-b border-sky-200/50 ghibli-crystal-surface ghibli-glow-enhanced">
      <div class={`container mx-auto px-3 sm:px-4 ${containerWidth} py-1 sm:py-2 flex justify-between items-center`}>
        <a href="/" class="text-2xl font-bold text-sky-700 flex items-center transition-transform hover:scale-105 ghibli-text-glow" style="font-family: var(--font-ghibli-heading);" data-ghibli-interactive>
          <span class="bg-clip-text text-transparent bg-gradient-to-r from-sky-600 to-sky-800 ghibli-sparkle">{siteConfig.name}</span>
        </a>
        
        <nav class="hidden md:flex space-x-6">
          {siteConfig.navigation.map(item => (
            <a href={item.href} class="text-earth-700 hover:text-sky-600 font-medium relative group transition-colors duration-300 ghibli-glow-enhanced" style="font-family: var(--font-ghibli-primary);" data-ghibli-interactive>
              {item.title}
              <span class="absolute -bottom-1 left-0 w-0 h-0.5 bg-sky-600 transition-all duration-300 group-hover:w-full ghibli-shimmer"></span>
            </a>
          ))}
        </nav>
        
        <div class="flex items-center space-x-4">
          <div class="relative hidden md:block">
            <input 
              type="text" 
              placeholder="Search articles..." 
              class="pl-10 pr-4 py-2 rounded-full border border-earth-300 focus:outline-none focus:ring-2 focus:ring-sky-500 focus:border-transparent shadow-sm transition-all duration-300 hover:shadow-md"
            />
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          </div>
          
          <!-- Mobile menu button -->
          <button class="md:hidden text-gray-700 hover:text-purple-600 transition-colors duration-300" id="mobile-menu-button">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
            </svg>
          </button>
        </div>
      </div>
      
      <!-- Mobile menu -->
      <div class="md:hidden hidden bg-white absolute w-full shadow-lg z-40" id="mobile-menu">
        <div class={`container mx-auto px-3 sm:px-4 ${containerWidth} py-3 space-y-3`}>
          <div class="relative">
            <input 
              type="text" 
              placeholder="Search articles..." 
              class="w-full pl-10 pr-4 py-2 rounded-full border border-earth-300 focus:outline-none focus:ring-2 focus:ring-sky-500 focus:border-transparent"
            />
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          </div>
          {siteConfig.navigation.map(item => (
            <a href={item.href} class="block text-earth-700 hover:text-sky-600 font-medium py-2 border-b border-gray-100 transition-colors duration-300">{item.title}</a>
          ))}
        </div>
      </div>
    </header>
    
    <main class="flex-grow relative z-0 ghibli-crystal-atmosphere ghibli-mouse-light">
      <slot />
    </main>
    
    <Footer />
    
    <!-- 2025 Ultra-Advanced Ghibli Effects (Inline for immediate loading) -->

    <script>
      // Essential Ghibli Effects Initialization
      document.addEventListener('DOMContentLoaded', () => {
        // Basic device detection
        const isLowEnd = navigator.hardwareConcurrency < 4;
        const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;

        // Apply performance classes
        if (isLowEnd) document.documentElement.classList.add('ghibli-reduced-effects');
        if (prefersReducedMotion) document.documentElement.classList.add('ghibli-no-motion');

        // Basic scroll animations
        const observerOptions = { threshold: 0.1, rootMargin: '0px 0px -50px 0px' };
        const scrollObserver = new IntersectionObserver((entries) => {
          entries.forEach(entry => {
            if (entry.isIntersecting) {
              entry.target.classList.add('animate-in');
            }
          });
        }, observerOptions);

        // Observe animated elements
        const animatedElements = document.querySelectorAll('[data-ghibli-animate], .ghibli-glow-enhanced');
        animatedElements.forEach(el => scrollObserver.observe(el));

        console.log('🌟 Ghibli Effects 2025 loaded successfully!');
      });

      // Mobile menu toggle
      const mobileMenuButton = document.getElementById('mobile-menu-button');
      const mobileMenu = document.getElementById('mobile-menu');
      
      if (mobileMenuButton && mobileMenu) {
        mobileMenuButton.addEventListener('click', () => {
          mobileMenu.classList.toggle('hidden');
        });
      }
      
      // Add shadow on scroll
      window.addEventListener('scroll', () => {
        const header = document.querySelector('header');
        if (header) {
          if (window.scrollY > 0) {
            header.classList.add('shadow-lg');
            header.classList.remove('shadow-md');
          } else {
            header.classList.remove('shadow-lg');
            header.classList.add('shadow-md');
          }
        }
      });
      
      // Check if user prefers dark mode or has it saved in local storage
      const isDark = localStorage.getItem('theme') === 'dark' || 
        (!('theme' in localStorage) && window.matchMedia('(prefers-color-scheme: dark)').matches);
      
      if (isDark) {
        document.documentElement.classList.add('dark');
        localStorage.setItem('theme', 'dark');
      } else {
        document.documentElement.classList.remove('dark');
        localStorage.setItem('theme', 'light');
      }
    </script>
  </body>
</html>