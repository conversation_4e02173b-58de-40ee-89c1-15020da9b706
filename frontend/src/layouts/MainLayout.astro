---
import '../styles/global.css';
import '../styles/ghibli-effects-2025.css';
import '../styles/performance-optimizations.css';
import '../styles/responsive-enhancements.css';
import { siteConfig } from '../config/site';
import Footer from '../components/Footer.astro';

interface Props {
  title?: string;
  description?: string;
  image?: string;
  containerWidth?: string;
}

const { 
  title = siteConfig.name,
  description = siteConfig.description,
  image = '/images/default-og.png',
  containerWidth = siteConfig.containerWidth 
} = Astro.props;

const canonicalURL = new URL(Astro.url.pathname, Astro.site);
---

<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>{title || siteConfig.name}</title>
    <meta name="description" content={description || siteConfig.description} />
    <link rel="canonical" href={canonicalURL} />
    
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website" />
    <meta property="og:url" content={canonicalURL} />
    <meta property="og:title" content={title} />
    <meta property="og:description" content={description} />
    <meta property="og:image" content={new URL(image, Astro.site)} />
    
    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image" />
    <meta property="twitter:url" content={canonicalURL} />
    <meta property="twitter:title" content={title} />
    <meta property="twitter:description" content={description} />
    <meta property="twitter:image" content={new URL(image, Astro.site)} />
    
    <!-- Enhanced Fonts for 2025 Ghibli Experience -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=Playfair+Display:wght@400;500;600;700;800;900&family=Comfortaa:wght@300;400;500;600;700&family=Nunito:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">

    <!-- Ghibli-inspired CSS Variables -->
    <style>
      :root {
        --font-ghibli-primary: 'Comfortaa', 'Inter', sans-serif;
        --font-ghibli-heading: 'Playfair Display', 'Comfortaa', serif;
        --font-ghibli-body: 'Nunito', 'Inter', sans-serif;
        --font-ghibli-accent: 'Inter', sans-serif;
      }
    </style>
    
    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
  </head>
  <body class="flex flex-col min-h-screen bg-sky-50 font-sans ghibli-dynamic-bg ghibli-particles-enhanced" style="font-family: var(--font-ghibli-body);">
    <header class="bg-sky-100 sticky top-0 z-50 shadow-md transition-all duration-300 backdrop-blur-sm bg-opacity-80 border-b border-sky-200/50 ghibli-crystal-surface ghibli-glow-enhanced">
      <div class={`container mx-auto px-3 sm:px-4 ${containerWidth} py-1 sm:py-2 flex justify-between items-center`}>
        <a href="/" class="text-2xl font-bold text-sky-700 flex items-center transition-transform hover:scale-105 ghibli-text-glow" style="font-family: var(--font-ghibli-heading);" data-ghibli-interactive>
          <span class="bg-clip-text text-transparent bg-gradient-to-r from-sky-600 to-sky-800 ghibli-sparkle">{siteConfig.name}</span>
        </a>
        
        <nav class="hidden md:flex space-x-6">
          {siteConfig.navigation.map(item => (
            <a href={item.href} class="text-earth-700 hover:text-sky-600 font-medium relative group transition-colors duration-300 ghibli-glow-enhanced" style="font-family: var(--font-ghibli-primary);" data-ghibli-interactive>
              {item.title}
              <span class="absolute -bottom-1 left-0 w-0 h-0.5 bg-sky-600 transition-all duration-300 group-hover:w-full ghibli-shimmer"></span>
            </a>
          ))}
        </nav>
        
        <div class="flex items-center space-x-4">
          <div class="relative hidden md:block">
            <input 
              type="text" 
              placeholder="Search articles..." 
              class="pl-10 pr-4 py-2 rounded-full border border-earth-300 focus:outline-none focus:ring-2 focus:ring-sky-500 focus:border-transparent shadow-sm transition-all duration-300 hover:shadow-md"
            />
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          </div>
          
          <!-- Mobile menu button -->
          <button class="md:hidden text-gray-700 hover:text-purple-600 transition-colors duration-300" id="mobile-menu-button">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
            </svg>
          </button>
        </div>
      </div>
      
      <!-- Mobile menu -->
      <div class="md:hidden hidden bg-white absolute w-full shadow-lg z-40" id="mobile-menu">
        <div class={`container mx-auto px-3 sm:px-4 ${containerWidth} py-3 space-y-3`}>
          <div class="relative">
            <input 
              type="text" 
              placeholder="Search articles..." 
              class="w-full pl-10 pr-4 py-2 rounded-full border border-earth-300 focus:outline-none focus:ring-2 focus:ring-sky-500 focus:border-transparent"
            />
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          </div>
          {siteConfig.navigation.map(item => (
            <a href={item.href} class="block text-earth-700 hover:text-sky-600 font-medium py-2 border-b border-gray-100 transition-colors duration-300">{item.title}</a>
          ))}
        </div>
      </div>
    </header>
    
    <main class="flex-grow relative z-0 ghibli-crystal-atmosphere ghibli-mouse-light">
      <slot />
    </main>
    
    <Footer />
    
    <!-- 2025 Ultra-Advanced Ghibli Effects -->
    <script src="/src/scripts/ghibli-interactions-2025.js" type="module"></script>
    <script src="/src/scripts/ghibli-testing-2025.js" type="module"></script>

    <script>
      // 2025 Advanced Device Detection and Performance Optimization
      document.addEventListener('DOMContentLoaded', () => {
        // Device capability detection
        const deviceCapabilities = {
          cores: navigator.hardwareConcurrency || 2,
          memory: navigator.deviceMemory || 2,
          connection: navigator.connection?.effectiveType || '4g',
          reducedMotion: window.matchMedia('(prefers-reduced-motion: reduce)').matches,
          highContrast: window.matchMedia('(prefers-contrast: high)').matches,
          touchDevice: 'ontouchstart' in window,
          retina: window.devicePixelRatio > 1.5
        };

        // Apply performance optimizations based on device capabilities
        if (deviceCapabilities.cores < 4 || deviceCapabilities.memory < 4) {
          document.documentElement.classList.add('ghibli-reduced-effects');
        }

        if (deviceCapabilities.connection === 'slow-2g' || deviceCapabilities.connection === '2g') {
          document.documentElement.classList.add('ghibli-emergency-mode');
        }

        if (deviceCapabilities.reducedMotion) {
          document.documentElement.classList.add('ghibli-no-motion');
        }

        if (deviceCapabilities.touchDevice) {
          document.documentElement.classList.add('ghibli-touch-device');
        }

        // Battery API optimization (if available)
        if ('getBattery' in navigator) {
          navigator.getBattery().then(battery => {
            if (battery.level < 0.2 || !battery.charging) {
              document.documentElement.classList.add('ghibli-reduced-effects');
            }
          });
        }

        // Intersection Observer for performance
        const performanceObserver = new IntersectionObserver((entries) => {
          entries.forEach(entry => {
            if (entry.isIntersecting) {
              entry.target.classList.add('ghibli-visible');
            } else {
              entry.target.classList.remove('ghibli-visible');
            }
          });
        }, { threshold: 0.1 });

        // Observe heavy effect elements
        const heavyElements = document.querySelectorAll('.ghibli-particles-enhanced, .ghibli-dynamic-bg, .ghibli-crystal-atmosphere');
        heavyElements.forEach(el => performanceObserver.observe(el));

        console.log('🌟 2025 Ghibli Effects optimized for device:', deviceCapabilities);
      });

      // Mobile menu toggle
      const mobileMenuButton = document.getElementById('mobile-menu-button');
      const mobileMenu = document.getElementById('mobile-menu');
      
      if (mobileMenuButton && mobileMenu) {
        mobileMenuButton.addEventListener('click', () => {
          mobileMenu.classList.toggle('hidden');
        });
      }
      
      // Add shadow on scroll
      window.addEventListener('scroll', () => {
        const header = document.querySelector('header');
        if (header) {
          if (window.scrollY > 0) {
            header.classList.add('shadow-lg');
            header.classList.remove('shadow-md');
          } else {
            header.classList.remove('shadow-lg');
            header.classList.add('shadow-md');
          }
        }
      });
      
      // Check if user prefers dark mode or has it saved in local storage
      const isDark = localStorage.getItem('theme') === 'dark' || 
        (!('theme' in localStorage) && window.matchMedia('(prefers-color-scheme: dark)').matches);
      
      if (isDark) {
        document.documentElement.classList.add('dark');
        localStorage.setItem('theme', 'dark');
      } else {
        document.documentElement.classList.remove('dark');
        localStorage.setItem('theme', 'light');
      }
    </script>
  </body>
</html>