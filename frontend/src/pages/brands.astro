---
import MainLayout from '../layouts/MainLayout.astro';
import { siteConfig } from '../config/site';
import Api, { BrandDetailResp } from '../lib/api';

const { containerWidth } = siteConfig;

// Get URL parameters for filtering and pagination
const url = Astro.url;
const filterChar = url.searchParams.get('filter') || 'all';
const searchQuery = url.searchParams.get('search') || '';
const currentPage = parseInt(url.searchParams.get('page') || '1');
const brandsPerPage = 24;

// Get all possible filter characters (A-Z + 0-9)
const filterOptions = [
  ...Array.from({ length: 26 }, (_, i) => String.fromCharCode(65 + i)), // A-Z
  ...Array.from({ length: 10 }, (_, i) => String(i)) // 0-9
];

// Fetch brands from the API
let allBrands: BrandDetailResp[] = [];
let totalBrands = 0;
let filteredBrands: BrandDetailResp[] = [];
let totalPages = 1;
let paginatedBrands: BrandDetailResp[] = [];

try {
  // Build API request parameters
  const apiParams: any = {
    page: currentPage,
    page_size: brandsPerPage,
    active: true
  };

  if (searchQuery) apiParams.search = searchQuery;
  if (filterChar !== 'all') apiParams.filter = filterChar;

  // Fetch brands from API
  const response = await Api.Brand.getBrandList(apiParams);

  totalBrands = response.total || 0;
  totalPages = Math.ceil(totalBrands / brandsPerPage);
  paginatedBrands = response.brand_list || [];

  // For filter count display purposes, fetch a sample of all brands
  if (filterChar !== 'all') {
    const allBrandsResponse = await Api.Brand.getBrandList({
      page: 1,
      page_size: 100,
      active: true
    });
    
    allBrands = allBrandsResponse.brand_list || [];
    
    // Apply letter filtering
    if (filterChar === '0-9') {
      // Filter for all brands starting with a number
      filteredBrands = allBrands.filter(brand => /^[0-9]/.test(brand.name));
    } else {
      // Filter for brands starting with the selected letter
      filteredBrands = allBrands.filter(brand => 
        brand.name.toUpperCase().startsWith(filterChar));
    }
    
    // Get just the brands for the current page
    paginatedBrands = filteredBrands.slice(
      (currentPage - 1) * brandsPerPage, 
      currentPage * brandsPerPage
    );
  } else {
    filteredBrands = paginatedBrands;
  }
} catch (error) {
  console.error('Failed to fetch brands:', error);
  totalBrands = 0;
  paginatedBrands = [];
  filteredBrands = [];
}

// Fetch popular brands for the featured section
let popularBrands: BrandDetailResp[] = [];
try {
  const response = await Api.Brand.getBrandList({
    featured: true,
    page_size: 6,
    active: true
  });
  
  popularBrands = response.brand_list || [];
} catch (error) {
  console.error('Failed to fetch popular brands:', error);
  popularBrands = [];
}

// Helper function to generate pagination URL
function getPaginationUrl(page, filter = filterChar) {
  const params = new URLSearchParams();
  if (filter !== 'all') params.set('filter', filter);
  if (page > 1) params.set('page', page.toString());
  return `${Astro.url.pathname}${params.toString() ? `?${params.toString()}` : ''}`;
}

// Helper function to generate filter URL
function getFilterUrl(filter) {
  return getPaginationUrl(1, filter);
}
---

<MainLayout
  title="Brands | BrandReviews"
  description="Browse product reviews, comparisons and buying guides for major tech brands, helping you make informed purchasing decisions."
>
  <!-- Enhanced Page Header with Ghibli Effects -->
  <section class="py-20 relative overflow-hidden ghibli-dynamic-bg ghibli-particles-enhanced" data-section="brands-header" style="background: linear-gradient(135deg, rgba(177, 180, 121, 0.9) 0%, rgba(142, 155, 94, 0.8) 50%, rgba(134, 115, 87, 0.9) 100%);">
    <!-- Parallax background layers -->
    <div class="absolute inset-0 ghibli-depth-blur"></div>
    <div class="absolute top-0 left-0 w-96 h-96 bg-gradient-radial from-white/20 to-transparent blur-3xl ghibli-float-physics"></div>
    <div class="absolute bottom-0 right-0 w-80 h-80 bg-gradient-radial from-white/15 to-transparent blur-3xl ghibli-float-physics" style="animation-delay: -4s;"></div>

    <!-- Floating brand-themed decorative elements -->
    <div class="absolute top-1/4 right-10 opacity-20 ghibli-float-physics">
      <svg width="60" height="60" viewBox="0 0 100 100" fill="none" class="text-white">
        <rect x="20" y="20" width="60" height="60" rx="8" fill="currentColor" opacity="0.4"/>
        <rect x="30" y="30" width="40" height="40" rx="4" fill="currentColor" opacity="0.6"/>
        <circle cx="50" cy="50" r="8" fill="currentColor" opacity="0.8"/>
      </svg>
    </div>
    <div class="absolute bottom-1/3 left-16 opacity-25 ghibli-float-physics" style="animation-delay: -2s;">
      <svg width="50" height="50" viewBox="0 0 100 100" fill="none" class="text-white">
        <path d="M50,10 Q80,30 90,50 Q80,70 50,90 Q20,70 10,50 Q20,30 50,10 Z" fill="currentColor" opacity="0.6"/>
      </svg>
    </div>

    <div class="container mx-auto px-4 max-w-6xl text-center relative z-10">
      <h1 class="text-5xl md:text-6xl font-bold mb-6 text-white drop-shadow-lg">
        Discover Brands
      </h1>
      <p class="text-xl md:text-2xl max-w-4xl mx-auto text-white/90 leading-relaxed mb-8">
        Explore our comprehensive collection of brand reviews, product comparisons, and buying guides
      </p>

      <!-- Enhanced search bar -->
      <div class="max-w-2xl mx-auto">
        <form method="GET" class="relative">
          <input
            type="text"
            name="search"
            value={searchQuery}
            placeholder="Search brands..."
            class="w-full pl-6 pr-16 py-4 rounded-2xl text-gray-900 border-0 focus:outline-none focus:ring-4 focus:ring-white/30 shadow-xl backdrop-blur-sm bg-white/95"
          />
          <button type="submit" class="absolute right-2 top-1/2 transform -translate-y-1/2 bg-ghibli-warm-600 hover:bg-ghibli-warm-700 text-white p-3 rounded-xl transition-all duration-300 shadow-lg hover:shadow-xl">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          </button>
        </form>
      </div>
    </div>
  </section>

  <!-- Enhanced Featured Brands Section -->
  {popularBrands.length > 0 && (
  <section class="py-20 relative overflow-hidden ghibli-glow-enhanced" data-section="featured-brands" style="background: linear-gradient(135deg, rgba(248, 250, 252, 0.95) 0%, rgba(255, 255, 255, 0.9) 100%);">
    <!-- Background effects -->
    <div class="absolute inset-0 ghibli-wind opacity-30"></div>
    <div class="absolute top-1/4 right-1/4 w-96 h-96 bg-gradient-radial from-ghibli-spring-200/20 to-transparent blur-3xl ghibli-float-physics"></div>
    <div class="absolute bottom-1/4 left-1/4 w-80 h-80 bg-gradient-radial from-ghibli-warm-200/15 to-transparent blur-3xl ghibli-float-physics" style="animation-delay: -6s;"></div>

    <div class="container mx-auto px-4 max-w-7xl relative z-10">
      <div class="text-center mb-12">
        <h2 class="text-4xl md:text-5xl font-bold text-ghibli-earth-800 mb-4">
          Featured Brands
        </h2>
        <p class="text-xl text-ghibli-earth-600 max-w-3xl mx-auto">
          Discover the most popular and trusted brands in our collection
        </p>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        {popularBrands.map((brand, index) => (
          <a href={`/brand/${brand.slug}`} class="group" data-brand-index={index}>
            <div class="ghibli-glow-enhanced bg-white/80 backdrop-blur-sm rounded-3xl overflow-hidden h-full flex flex-col border border-ghibli-earth-200 shadow-lg hover:shadow-2xl transition-all duration-500 hover:scale-105">
              <div class="relative pb-[60%] overflow-hidden">
                <div class="absolute inset-0 flex items-center justify-center bg-gradient-to-br from-ghibli-spring-50 to-ghibli-warm-50">
                  <img
                    src={brand.logo || `https://images.unsplash.com/photo-1611224923853-80b023f02d71?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80`}
                    alt={brand.name}
                    class="max-w-[70%] max-h-[70%] object-contain transition-transform duration-700 group-hover:scale-110"
                  />
                </div>

                <!-- Brand category badge -->
                <div class="absolute top-4 left-4 bg-gradient-to-r from-ghibli-warm-600 to-ghibli-warm-700 text-white text-sm font-semibold px-4 py-2 rounded-2xl shadow-lg">
                  Featured
                </div>
              </div>

              <div class="p-8 flex-grow flex flex-col">
                <h3 class="text-2xl font-bold mb-3 text-ghibli-earth-800 group-hover:text-ghibli-warm-700 transition-colors duration-300">
                  {brand.name}
                </h3>

                <p class="text-ghibli-earth-600 mb-6 text-lg leading-relaxed flex-grow">
                  {brand.description || `Explore our comprehensive collection of ${brand.name} product reviews and expert buying guides.`}
                </p>

                <div class="mt-auto flex justify-between items-center">
                  <span class="inline-flex items-center text-ghibli-warm-600 font-semibold group-hover:text-ghibli-warm-700 transition-all duration-300 group-hover:translate-x-2">
                    Explore Brand
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-2 transition-transform duration-300 group-hover:translate-x-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3" />
                    </svg>
                  </span>

                  <!-- Article count -->
                  <div class="flex items-center text-ghibli-earth-500 text-sm">
                    <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    {brand.article_count || 0} articles
                  </div>
                </div>
              </div>
            </div>
          </a>
        ))}
      </div>
    </div>
  </section>
  )}

  <!-- Enhanced Brands Catalog Section -->
  <section class="py-20 relative overflow-hidden ghibli-mouse-light" data-section="brands-catalog" style="background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(254, 252, 232, 0.9) 100%);">
    <!-- Background effects -->
    <div class="absolute inset-0 ghibli-fireflies opacity-20"></div>
    <div class="absolute top-1/3 left-1/3 w-96 h-96 bg-gradient-radial from-ghibli-earth-200/15 to-transparent blur-3xl ghibli-float-physics"></div>
    <div class="absolute bottom-1/3 right-1/3 w-80 h-80 bg-gradient-radial from-ghibli-spring-200/12 to-transparent blur-3xl ghibli-float-physics" style="animation-delay: -8s;"></div>

    <div class="container mx-auto px-4 max-w-7xl relative z-10">
      <div class="mb-12 text-center">
        <h2 class="text-4xl md:text-5xl font-bold text-ghibli-earth-800 mb-4">
          All Brands
          {filterChar !== 'all' && (
            <span class="text-ghibli-warm-600"> - {filterChar}</span>
          )}
        </h2>
        <p class="text-xl text-ghibli-earth-600 max-w-4xl mx-auto leading-relaxed">
          Browse our comprehensive collection of brand reviews, product comparisons, and buying guides to find the perfect tech products for your needs
        </p>
      </div>

      <!-- Enhanced Alphabet filter -->
      <div class="mb-12">
        <div class="flex flex-wrap justify-center gap-2 mb-4">
          <a
            href={getFilterUrl('all')}
            class={`filter-link-enhanced ${filterChar === 'all' ? 'active' : ''}`}
          >
            All Brands
          </a>

          {filterOptions.map(char => (
            <a
              href={getFilterUrl(char)}
              class={`filter-link-enhanced ${filterChar === char ? 'active' : ''}`}
            >
              {char}
            </a>
          ))}

          <a
            href={getFilterUrl('0-9')}
            class={`filter-link-enhanced ${filterChar === '0-9' ? 'active' : ''}`}
          >
            0-9
          </a>
        </div>

        <!-- Filter info -->
        <div class="text-center">
          <p class="text-ghibli-earth-500 font-medium">
            {totalBrands > 0 ? (
              <>Showing {paginatedBrands.length} of {totalBrands} brands</>
            ) : (
              <>No brands found</>
            )}
            {searchQuery && (
              <> for "{searchQuery}"</>
            )}
          </p>
        </div>
      </div>
      </div>

      {paginatedBrands.length > 0 ? (
        <div>
          <!-- Enhanced Brands grid -->
          <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-6 mb-16">
            {paginatedBrands.map((brand, index) => (
              <a
                href={`/brand/${brand.slug}`}
                class="ghibli-glow-enhanced group bg-white/80 backdrop-blur-sm rounded-3xl shadow-lg hover:shadow-2xl p-6 flex flex-col items-center justify-center text-center border border-ghibli-earth-200 transition-all duration-500 hover:scale-105"
                data-brand-grid-index={index}
              >
                <div class="w-full h-24 flex items-center justify-center mb-4 relative">
                  <img
                    src={brand.logo || `https://images.unsplash.com/photo-1611224923853-80b023f02d71?ixlib=rb-4.0.3&auto=format&fit=crop&w=200&q=80`}
                    alt={brand.name}
                    class="max-h-full max-w-[85%] object-contain transition-transform duration-700 group-hover:scale-125"
                  />

                  <!-- Hover overlay -->
                  <div class="absolute inset-0 bg-gradient-to-t from-ghibli-warm-500/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-2xl"></div>
                </div>

                <h3 class="text-sm font-semibold text-ghibli-earth-800 group-hover:text-ghibli-warm-700 transition-colors duration-300 mb-2">
                  {brand.name}
                </h3>

                <!-- Article count indicator -->
                <div class="flex items-center text-xs text-ghibli-earth-500 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                  <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  {brand.article_count || 0} articles
                </div>
              </a>
            ))}
          </div>
          
          <!-- Enhanced Pagination -->
          {totalPages > 1 && (
            <div class="flex justify-center">
              <nav class="inline-flex items-center gap-2" aria-label="Pagination">
                {currentPage > 1 && (
                  <a
                    href={getPaginationUrl(currentPage - 1)}
                    class="inline-flex items-center px-4 py-3 bg-white/80 backdrop-blur-sm border border-ghibli-earth-200 rounded-2xl text-ghibli-earth-700 hover:bg-ghibli-earth-50 hover:border-ghibli-earth-300 transition-all duration-300 shadow-sm hover:shadow-md font-medium"
                    aria-label="Previous page"
                  >
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
                    </svg>
                    Previous
                  </a>
                )}

                <div class="flex items-center gap-1">
                  {Array.from({length: Math.min(totalPages, 7)}, (_, i) => {
                    let page;
                    if (totalPages <= 7) {
                      page = i + 1;
                    } else if (currentPage <= 4) {
                      page = i + 1;
                    } else if (currentPage >= totalPages - 3) {
                      page = totalPages - 6 + i;
                    } else {
                      page = currentPage - 3 + i;
                    }

                    return (
                      <a
                        href={getPaginationUrl(page)}
                        class={`w-12 h-12 flex items-center justify-center rounded-2xl font-semibold transition-all duration-300 ${
                          page === currentPage
                            ? 'bg-gradient-to-r from-ghibli-warm-600 to-ghibli-warm-700 text-white shadow-lg transform scale-110'
                            : 'bg-white/80 backdrop-blur-sm border border-ghibli-earth-200 text-ghibli-earth-700 hover:bg-ghibli-earth-50 hover:border-ghibli-earth-300 shadow-sm hover:shadow-md'
                        }`}
                      >
                        {page}
                      </a>
                    );
                  })}
                </div>

                {currentPage < totalPages && (
                  <a
                    href={getPaginationUrl(currentPage + 1)}
                    class="inline-flex items-center px-4 py-3 bg-white/80 backdrop-blur-sm border border-ghibli-earth-200 rounded-2xl text-ghibli-earth-700 hover:bg-ghibli-earth-50 hover:border-ghibli-earth-300 transition-all duration-300 shadow-sm hover:shadow-md font-medium"
                    aria-label="Next page"
                  >
                    Next
                    <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                    </svg>
                  </a>
                )}
              </nav>
            </div>
          )}
        </div>
      ) : (
        <div class="ghibli-glow-enhanced bg-white/80 backdrop-blur-sm p-16 rounded-3xl text-center border border-ghibli-earth-200 shadow-xl">
          <div class="mb-8">
            <svg class="w-20 h-20 mx-auto text-ghibli-earth-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <h3 class="text-3xl font-bold text-ghibli-earth-800 mb-4">No brands found</h3>
          <p class="text-xl text-ghibli-earth-600 mb-8 max-w-md mx-auto leading-relaxed">
            {filterChar !== 'all'
              ? `No brands starting with "${filterChar}" were found.`
              : searchQuery
                ? `No brands found matching "${searchQuery}".`
                : 'No brands are available at the moment.'
            }
          </p>
          {(filterChar !== 'all' || searchQuery) && (
            <a href="/brands" class="inline-flex items-center bg-gradient-to-r from-ghibli-warm-600 to-ghibli-warm-700 text-white font-semibold px-6 py-3 rounded-2xl transition-all duration-300 hover:shadow-lg hover:scale-105">
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
              </svg>
              View all brands
            </a>
          )}
        </div>
      )}
    </div>
  </section>
</MainLayout>

<style>
  /* Enhanced Brands Page Styling */
  .filter-link-enhanced {
    display: inline-block;
    padding: 0.75rem 1.25rem;
    font-size: 0.875rem;
    font-weight: 600;
    color: #6b7280;
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(142, 155, 94, 0.2);
    border-radius: 1.5rem;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    margin: 0.25rem;
    min-width: 3rem;
    text-align: center;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  }

  .filter-link-enhanced:hover {
    background: rgba(142, 155, 94, 0.1);
    border-color: rgba(142, 155, 94, 0.3);
    color: #374151;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  }

  .filter-link-enhanced.active {
    background: linear-gradient(135deg, #8e9b5e 0%, #b1b479 100%);
    color: white;
    border-color: #8e9b5e;
    transform: translateY(-2px) scale(1.05);
    box-shadow: 0 6px 12px rgba(142, 155, 94, 0.3);
  }

  /* Scroll animations */
  [data-brand-index],
  [data-brand-grid-index] {
    opacity: 0;
    transform: translateY(20px);
    transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
  }

  [data-brand-index].animate-in,
  [data-brand-grid-index].animate-in {
    opacity: 1;
    transform: translateY(0);
  }

  /* Staggered animation delays for featured brands */
  [data-brand-index="0"].animate-in { transition-delay: 0ms; }
  [data-brand-index="1"].animate-in { transition-delay: 100ms; }
  [data-brand-index="2"].animate-in { transition-delay: 200ms; }
  [data-brand-index="3"].animate-in { transition-delay: 300ms; }
  [data-brand-index="4"].animate-in { transition-delay: 400ms; }
  [data-brand-index="5"].animate-in { transition-delay: 500ms; }

  /* Staggered animation delays for brand grid */
  [data-brand-grid-index="0"].animate-in { transition-delay: 0ms; }
  [data-brand-grid-index="1"].animate-in { transition-delay: 50ms; }
  [data-brand-grid-index="2"].animate-in { transition-delay: 100ms; }
  [data-brand-grid-index="3"].animate-in { transition-delay: 150ms; }
  [data-brand-grid-index="4"].animate-in { transition-delay: 200ms; }
  [data-brand-grid-index="5"].animate-in { transition-delay: 250ms; }
  [data-brand-grid-index="6"].animate-in { transition-delay: 300ms; }
  [data-brand-grid-index="7"].animate-in { transition-delay: 350ms; }
  [data-brand-grid-index="8"].animate-in { transition-delay: 400ms; }
  [data-brand-grid-index="9"].animate-in { transition-delay: 450ms; }
  [data-brand-grid-index="10"].animate-in { transition-delay: 500ms; }
  [data-brand-grid-index="11"].animate-in { transition-delay: 550ms; }

  /* Enhanced hover effects */
  .ghibli-glow-enhanced:hover {
    transform: translateY(-4px) scale(1.02);
  }

  /* Smooth scrolling */
  html {
    scroll-behavior: smooth;
  }

  /* Custom scrollbar */
  ::-webkit-scrollbar {
    width: 8px;
  }

  ::-webkit-scrollbar-track {
    background: rgba(248, 250, 252, 0.5);
  }

  ::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, rgba(142, 155, 94, 0.6), rgba(177, 180, 121, 0.6));
    border-radius: 4px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, rgba(142, 155, 94, 0.8), rgba(177, 180, 121, 0.8));
  }

  /* Responsive adjustments */
  @media (max-width: 768px) {
    .filter-link-enhanced {
      padding: 0.5rem 1rem;
      font-size: 0.8rem;
      min-width: 2.5rem;
    }

    .ghibli-float-physics {
      animation-duration: 8s;
    }
  }

  @media (prefers-reduced-motion: reduce) {
    .ghibli-float-physics,
    .ghibli-particles-enhanced,
    .ghibli-dynamic-bg {
      animation: none;
    }

    [data-brand-index],
    [data-brand-grid-index] {
      transition: none;
    }
  }
</style>

<script>
  // Enhanced Brands Page Interactions
  document.addEventListener('DOMContentLoaded', () => {
    // Intersection Observer for scroll animations
    const observerOptions = {
      threshold: 0.1,
      rootMargin: '0px 0px -50px 0px'
    };

    const brandObserver = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          entry.target.classList.add('animate-in');
        }
      });
    }, observerOptions);

    // Observe all brand cards
    const featuredBrands = document.querySelectorAll('[data-brand-index]');
    const gridBrands = document.querySelectorAll('[data-brand-grid-index]');

    featuredBrands.forEach(brand => brandObserver.observe(brand));
    gridBrands.forEach(brand => brandObserver.observe(brand));

    // Mouse tracking for enhanced glow effects
    let mouseX = 0;
    let mouseY = 0;

    document.addEventListener('mousemove', (e) => {
      mouseX = e.clientX;
      mouseY = e.clientY;

      // Update CSS custom properties for mouse-following effects
      document.documentElement.style.setProperty('--mouse-x', `${(mouseX / window.innerWidth) * 100}%`);
      document.documentElement.style.setProperty('--mouse-y', `${(mouseY / window.innerHeight) * 100}%`);
    });

    // Enhanced filter link interactions
    const filterLinks = document.querySelectorAll('.filter-link-enhanced');
    filterLinks.forEach(link => {
      link.addEventListener('mouseenter', () => {
        link.style.transform = 'translateY(-3px) scale(1.05)';
      });

      link.addEventListener('mouseleave', () => {
        if (!link.classList.contains('active')) {
          link.style.transform = 'translateY(0) scale(1)';
        }
      });
    });

    // Performance monitoring
    if ('requestIdleCallback' in window) {
      requestIdleCallback(() => {
        console.log('Enhanced Brands page loaded successfully');
      });
    }
  });
</script>