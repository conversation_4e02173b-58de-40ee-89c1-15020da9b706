---
import MainLayout from '../layouts/MainLayout.astro';
import Api, { ArticleDetailResp, CategoryDetailResp, TagDetailResp } from '../lib/api';

// Fetch articles from API
let allArticles: ArticleDetailResp[] = [];
let totalArticles = 0;
let currentPage = 1;
let totalPages = 1;

try {
  // Get URL parameters for filtering and pagination
  const url = Astro.url;
  const selectedCategory = url.searchParams.get('category') || '';
  const selectedBrand = url.searchParams.get('brand') || '';
  const selectedTag = url.searchParams.get('tag') || '';
  const searchQuery = url.searchParams.get('search') || '';
  currentPage = parseInt(url.searchParams.get('page') || '1');
  const articlesPerPage = 6;

  // Build API request parameters
  const apiParams: any = {
    page: currentPage,
    page_size: articlesPerPage,
  };

  if (selectedCategory) apiParams.category = selectedCategory;
  if (selectedBrand) apiParams.brand = selectedBrand;
  if (searchQuery) apiParams.search = searchQuery;
  if (selectedTag) apiParams.tag = selectedTag;

  // Fetch articles from API
  const articleData = await Api.Article.getArticleList(apiParams);
  allArticles = articleData.article_list || [];
  totalArticles = articleData.total || 0;
  totalPages = Math.ceil(totalArticles / articlesPerPage);

} catch (error) {
  console.error('Failed to fetch articles:', error);
  allArticles = [];
}
// Fetch categories from API
let allCategories: CategoryDetailResp[] = [];
try {
  const categoryData = await Api.Category.getCategoryList({ page_size: 50 });
  allCategories = categoryData.category_list || [];
} catch (error) {
  console.error('Failed to fetch categories:', error);
  allCategories = [];
}

// Fetch popular tags from API
let popularTags: TagDetailResp[] = [];
try {
  const tagData = await Api.Tag.getTagList({ page_size: 20 });
  popularTags = tagData.tag_list || [];
} catch (error) {
  console.error('Failed to fetch tags:', error);
  popularTags = [];
}
// Get URL parameters for filtering
const url = Astro.url;
const selectedCategory = url.searchParams.get('category') || '';
const selectedBrand = url.searchParams.get('brand') || '';
const selectedTag = url.searchParams.get('tag') || '';
const searchQuery = url.searchParams.get('search') || '';

// Get unique brands from articles for filter
const brands = [...new Set(allArticles.map(article => article.brand?.name || 'Unknown'))].filter(brand => brand !== 'Unknown').sort();

// Helper function to generate URL with params
function getFilterUrl(params: Record<string, string> = {}) {
  const newParams = new URLSearchParams();

  // Add existing params except page (we reset to page 1 when changing filters)
  if (selectedBrand && !('brand' in params)) newParams.set('brand', selectedBrand);
  if (selectedCategory && !('category' in params)) newParams.set('category', selectedCategory);
  if (selectedTag && !('tag' in params)) newParams.set('tag', selectedTag);
  if (searchQuery && !('search' in params)) newParams.set('search', searchQuery);

  // Add new params
  Object.entries(params).forEach(([key, value]) => {
    if (value) newParams.set(key, value);
  });

  return `${Astro.url.pathname}${newParams.toString() ? `?${newParams.toString()}` : ''}`;
}

// Helper function for pagination URLs
function getPaginationUrl(page: number) {
  const params = new URLSearchParams();
  if (selectedBrand) params.set('brand', selectedBrand);
  if (selectedCategory) params.set('category', selectedCategory);
  if (selectedTag) params.set('tag', selectedTag);
  if (searchQuery) params.set('search', searchQuery);
  if (page > 1) params.set('page', page.toString());
  return `${Astro.url.pathname}${params.toString() ? `?${params.toString()}` : ''}`;
}

// Format date helper
function formatDate(dateString: string) {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
}
---

<MainLayout
  title={selectedBrand ? `${selectedBrand} Articles | SmartReviews` : "All Articles | SmartReviews"}
  description="Browse our comprehensive collection of product reviews, buying guides, and tech comparisons to make informed purchasing decisions."
>
  <!-- Page Header -->
  <section class="py-12 bg-gradient-to-r from-purple-700 to-purple-800 text-white">
    <div class="container mx-auto px-4 max-w-5xl text-center">
      <h1 class="text-4xl md:text-5xl font-bold mb-4">
        {selectedBrand ? `${selectedBrand} Articles` : "All Articles"}
      </h1>
      <p class="text-xl max-w-3xl mx-auto">Explore our in-depth reviews, guides, and comparisons to help you make informed purchasing decisions.</p>
    </div>
  </section>
  
  <!-- Filter Navigation -->
  <section class="py-6 bg-gray-50 border-b border-gray-200 sticky top-0 z-20">
    <div class="container mx-auto px-4 max-w-5xl">
      <!-- Active filters display -->
      {(selectedBrand || selectedCategory || selectedTag) && (
        <div class="mb-4 flex items-center gap-2">
          <span class="text-gray-700">Active filters:</span>
          {selectedBrand && (
            <div class="inline-flex items-center bg-purple-100 text-purple-800 px-3 py-1 rounded-full text-sm">
              <span>Brand: {selectedBrand}</span>
              <a href={getFilterUrl({brand: ''})} class="ml-2 text-purple-600 hover:text-purple-800">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
              </a>
            </div>
          )}
          {selectedCategory && (
            <div class="inline-flex items-center bg-purple-100 text-purple-800 px-3 py-1 rounded-full text-sm">
              <span>Category: {selectedCategory}</span>
              <a href={getFilterUrl({category: ''})} class="ml-2 text-purple-600 hover:text-purple-800">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
              </a>
            </div>
          )}
          {selectedTag && (
            <div class="inline-flex items-center bg-purple-100 text-purple-800 px-3 py-1 rounded-full text-sm">
              <span>Tag: {selectedTag}</span>
              <a href={getFilterUrl({tag: ''})} class="ml-2 text-purple-600 hover:text-purple-800">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
              </a>
            </div>
          )}
          <a href="/articles" class="text-purple-600 hover:text-purple-800 text-sm">
            Clear all
          </a>
        </div>
      )}
    
      <!-- Category filters -->
      <div class="flex flex-wrap items-center gap-3">
        <span class="font-medium text-gray-700">Categories:</span>
        <a 
          href={getFilterUrl({category: ''})} 
          class={`px-3 py-1 rounded-full text-sm transition-colors ${!selectedCategory 
            ? 'bg-purple-600 text-white' 
            : 'bg-white border border-gray-300 text-gray-700 hover:bg-gray-100'}`}
        >
          All
        </a>
        {allCategories.map(category => (
          <a 
            href={getFilterUrl({category: category.name})}
            class={`px-3 py-1 rounded-full text-sm transition-colors ${selectedCategory === category.name 
              ? 'bg-purple-600 text-white' 
              : 'bg-white border border-gray-300 text-gray-700 hover:bg-gray-100'}`}
          >
            {category.name}
          </a>
        ))}
      </div>
    </div>
  </section>
  
  <!-- Articles List -->
  <section class="py-12 bg-white">
    <div class="container mx-auto px-4 max-w-5xl">
      <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
        <!-- Main Content -->
        <div class="md:col-span-2">
          <div class="mb-8 flex justify-between items-center">
            <h2 class="text-2xl font-bold">
              {selectedBrand ? `${selectedBrand} Articles` : "All Articles"}
              {selectedCategory ? ` - ${selectedCategory}` : ""}
              {selectedTag ? ` - ${selectedTag}` : ""}
            </h2>
            <p class="text-gray-500">
              Showing {paginatedArticles.length} of {totalArticles} articles
            </p>
          </div>
          
          {totalArticles === 0 ? (
            <div class="card p-8 text-center">
              <p class="text-lg text-gray-600 mb-4">No articles found matching your filters.</p>
              <a href="/articles" class="text-purple-600 hover:text-purple-800 font-medium">View all articles</a>
            </div>
          ) : (
          <div class="space-y-8">
              {paginatedArticles.map(article => (
                <article class="card flex flex-col md:flex-row overflow-hidden hover:shadow-md transition-shadow">
                <div class="md:w-1/3 relative">
                  <img 
                    src={article.image} 
                    alt={article.title}
                    class="w-full h-full object-cover"
                  />
                    <span class="absolute top-4 left-4 bg-purple-600 text-white text-sm font-medium px-3 py-1 rounded-full">
                    {article.category}
                  </span>
                </div>
                <div class="md:w-2/3 p-6">
                  <h3 class="text-xl font-bold mb-3">
                      <a href={`/article/${article.slug}`} class="text-gray-900 hover:text-purple-600">
                      {article.title}
                    </a>
                  </h3>
                  <p class="text-gray-600 mb-4">{article.excerpt}</p>
                    <div class="flex flex-wrap justify-between items-center text-sm text-gray-500 gap-y-2">
                      <div class="flex items-center gap-2">
                    <span>{article.publishedDate}</span>
                    <span>By {article.author}</span>
                      </div>
                      <a 
                        href={getFilterUrl({brand: article.brand})} 
                        class="inline-flex items-center bg-gray-100 hover:bg-gray-200 px-3 py-1 rounded-full text-sm transition-colors"
                      >
                        <span class="text-gray-700 font-medium">{article.brand}</span>
                      </a>
                    </div>
                    
                    <!-- Tags -->
                    <div class="mt-4 flex flex-wrap gap-2">
                      {article.tags.map(tag => (
                        <a 
                          href={getFilterUrl({tag: tag.toLowerCase().replace(/\s+/g, '-')})} 
                          class={`text-xs px-2 py-1 rounded-full transition-colors ${
                            selectedTag === tag.toLowerCase().replace(/\s+/g, '-')
                              ? 'bg-purple-100 text-purple-800 font-medium'
                              : 'bg-gray-100 hover:bg-gray-200 text-gray-700'
                          }`}
                        >
                          #{tag}
                        </a>
                      ))}
                  </div>
                </div>
              </article>
            ))}
          </div>
          )}
          
          <!-- Pagination -->
          {totalPages > 1 && (
          <div class="mt-12 flex justify-center">
            <nav class="inline-flex rounded-md shadow">
                {currentPage > 1 && (
                  <a href={getPaginationUrl(currentPage - 1)} class="py-2 px-4 border border-gray-300 bg-white rounded-l-md text-gray-700 hover:bg-gray-50">
                Previous
              </a>
                )}
                
                {Array.from({length: totalPages}, (_, i) => i + 1).map(page => (
                  <a 
                    href={getPaginationUrl(page)} 
                    class={`py-2 px-4 border-t border-b border-l border-gray-300 ${
                      page === currentPage 
                        ? 'bg-purple-50 text-purple-700 font-medium' 
                        : 'bg-white text-gray-700 hover:bg-gray-50'
                    }`}
                  >
                    {page}
                  </a>
                ))}
                
                {currentPage < totalPages && (
                  <a href={getPaginationUrl(currentPage + 1)} class="py-2 px-4 border border-gray-300 bg-white rounded-r-md text-gray-700 hover:bg-gray-50">
                Next
              </a>
                )}
            </nav>
          </div>
          )}
        </div>
        
        <!-- Sidebar -->
        <div class="md:col-span-1">
          <!-- Search -->
          <div class="card p-6 mb-8">
            <h3 class="text-xl font-bold mb-4">Search Articles</h3>
            <div class="relative">
              <input 
                type="text" 
                placeholder="Search for articles..." 
                class="w-full pl-4 pr-10 py-2 rounded-md border border-gray-300 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
              />
              <button class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </button>
            </div>
          </div>
          
          <!-- Brand Filter -->
          <div class="card p-6 mb-8">
            <h3 class="text-xl font-bold mb-4">Filter by Brand</h3>
            
            <!-- Brand search -->
            <div class="relative mb-4">
              <input 
                type="text" 
                id="brandSearch"
                placeholder="Search brands..." 
                class="w-full pl-4 pr-10 py-2 rounded-md border border-gray-300 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
              />
              <button class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </button>
            </div>
            
            <!-- Popular Brands (limit to 5-10) -->
            <div class="mb-2">
              <h4 class="font-medium text-gray-700 mb-2 text-sm">Popular Brands</h4>
              <div class="space-y-1">
                <a 
                  href={getFilterUrl({brand: ''})} 
                  class={`block w-full text-left px-3 py-2 rounded-md text-sm ${!selectedBrand 
                    ? 'bg-purple-100 text-purple-800 font-medium' 
                    : 'text-gray-700 hover:bg-gray-100'}`}
                >
                  All Brands
                </a>
                {brands.slice(0, 8).map(brand => (
                  <a 
                    href={getFilterUrl({brand: brand})}
                    class={`block w-full text-left px-3 py-2 rounded-md text-sm ${selectedBrand === brand 
                      ? 'bg-purple-100 text-purple-800 font-medium' 
                      : 'text-gray-700 hover:bg-gray-100'}`}
                  >
                    {brand}
                </a>
              ))}
            </div>
            </div>
            
            <!-- View all brands link -->
            <a 
              href="/brands" 
              class="text-purple-600 hover:text-purple-800 text-sm font-medium flex items-center"
            >
              Browse all brands
              <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
              </svg>
            </a>
          </div>
          
          <!-- Popular Tags -->
          <div class="card p-6 mb-8">
            <h3 class="text-xl font-bold mb-4">Popular Tags</h3>
            <div class="flex flex-wrap gap-2">
              {popularTags.map(tag => (
                <a 
                  href={`/articles?tag=${tag.slug}`}
                  class={`text-sm px-3 py-1 rounded-full transition-colors ${
                    selectedTag === tag.slug
                      ? 'bg-purple-600 text-white'
                      : 'bg-gray-100 hover:bg-gray-200 text-gray-700'
                  }`}
                >
                  {tag.name}
                </a>
              ))}
              <a href="/tags" class="text-purple-600 hover:text-purple-800 text-sm flex items-center ml-1">
                View all tags
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                </svg>
              </a>
            </div>
          </div>
          
          <!-- Newsletter -->
          <div class="card p-6 bg-gradient-to-br from-purple-50 to-purple-100 border border-purple-100">
            <h3 class="text-xl font-bold mb-4">Stay Updated</h3>
            <p class="text-gray-600 mb-4">Subscribe to our newsletter to receive the latest product reviews and buying guides.</p>
            <form class="space-y-4">
              <div>
                <input 
                  type="email" 
                  placeholder="Your email address" 
                  class="w-full px-4 py-2 rounded-md border border-gray-300 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                  required
                />
              </div>
              <button 
                type="submit" 
                class="w-full bg-purple-600 hover:bg-purple-700 text-white font-medium py-2 px-4 rounded-md transition-colors"
              >
                Subscribe
              </button>
            </form>
          </div>
        </div>
      </div>
    </div>
  </section>
  
  <!-- CTA -->
  <section class="py-16 bg-gradient-to-r from-purple-700 to-purple-800 text-white">
    <div class="container mx-auto px-4 max-w-5xl text-center">
      <h2 class="text-3xl md:text-4xl font-bold mb-6">Looking for Something Specific?</h2>
      <p class="text-xl mb-8 max-w-3xl mx-auto">Use our advanced search to find exactly what you're looking for.</p>
      <div class="max-w-xl mx-auto relative">
        <input 
          type="text" 
          placeholder="Search for products, brands, or categories..." 
          class="w-full pl-5 pr-16 py-4 rounded-full text-gray-900 border border-gray-300 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
        />
        <button class="absolute right-2 top-1/2 transform -translate-y-1/2 bg-purple-600 hover:bg-purple-700 text-white p-3 rounded-full">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
          </svg>
        </button>
      </div>
    </div>
  </section>
</MainLayout>

<script>
  // Brand search functionality
  document.addEventListener('DOMContentLoaded', () => {
    const brandSearchInput = document.getElementById('brandSearch') as HTMLInputElement;
    if (brandSearchInput) {
      const allBrands = [
        'Apple', 'Samsung', 'Sony', 'Asus', 'LG', 'Google', 
        'Xiaomi', 'Huawei', 'Dell', 'Logitech', 'Amazon', 'Microsoft',
        'Razer', 'HP', 'Acer', 'Lenovo', 'Canon', 'Nikon', 'Intel', 'AMD',
        // Add more brand names as needed
      ];
      
      const brandParent = brandSearchInput.closest('.card');
      if (!brandParent) return;
      
      const brandContainer = brandParent.querySelector('.space-y-1');
      if (!brandContainer) return;
      
      const defaultDisplay = brandContainer.innerHTML;
      const parentElement = brandSearchInput.parentElement;
      if (!parentElement) return;
      
      const clearButton = parentElement.querySelector('button');
      if (!clearButton) return;

      // Handle search input
      brandSearchInput.addEventListener('input', () => {
        const searchTerm = brandSearchInput.value.toLowerCase();
        
        // Reset to default display if search is empty
        if (!searchTerm) {
          brandContainer.innerHTML = defaultDisplay;
          return;
        }
        
        // Filter brands based on search term
        const filteredBrands = allBrands.filter(brand => 
          brand.toLowerCase().includes(searchTerm)
        );
        
        // Build HTML for filtered results
        if (filteredBrands.length > 0) {
          let html = `
            <a 
              href="?brand=" 
              class="block w-full text-left px-3 py-2 rounded-md text-sm text-gray-700 hover:bg-gray-100"
            >
              All Brands
            </a>
          `;
          
          filteredBrands.forEach(brand => {
            const isSelected = new URLSearchParams(window.location.search).get('brand') === brand;
            html += `
              <a 
                href="?brand=${encodeURIComponent(brand)}" 
                class="block w-full text-left px-3 py-2 rounded-md text-sm ${
                  isSelected ? 'bg-purple-100 text-purple-800 font-medium' : 'text-gray-700 hover:bg-gray-100'
                }"
              >
                ${brand}
              </a>
            `;
          });
          
          brandContainer.innerHTML = html;
        } else {
          brandContainer.innerHTML = `
            <p class="text-gray-500 text-sm px-3 py-2">No brands found matching "${searchTerm}"</p>
            <a 
              href="?brand=" 
              class="block w-full text-left px-3 py-2 rounded-md text-sm text-gray-700 hover:bg-gray-100"
            >
              All Brands
            </a>
          `;
        }
      });
      
      // Clear search when clicking the X button
      clearButton.addEventListener('click', () => {
        brandSearchInput.value = '';
        brandContainer.innerHTML = defaultDisplay;
      });
    }
  });
</script>