---
import MainLayout from '../layouts/MainLayout.astro';
import Hero from '../components/Hero.astro';
import Footer from '../components/Footer.astro';
import FeaturedCarousel from '../components/FeaturedCarousel.astro';
import DealsSection from '../components/DealsSection.astro';
import TagList from '../components/TagList.astro';
import { siteConfig } from '../config/site';
import Api, { TagDetailResp, CategoryDetailResp, ArticleDetailResp, BrandDetailResp, DealDetailResp } from '../lib/api';
import CategoryGrid from '../components/CategoryGrid.astro';
import Newsletter from '../components/Newsletter.astro';

const { containerWidth } = siteConfig;

// Fetch categories from API
let categories: CategoryDetailResp[] = [];
try {
  const categoryData = await Api.Category.getCategoryList({ featured: true, page_size: 6 });
  categories = categoryData.category_list || [];
} catch (error) {
  console.error('Failed to fetch categories:', error);
  categories = [];
}

// Fetch popular tags from API
let popularTags: TagDetailResp[] = [];
try {
  const tagData = await Api.Tag.getTagList({ featured: true, page_size: 20 });
  popularTags = tagData.tag_list || [];
} catch (error) {
  console.error('Failed to fetch tags:', error);
  popularTags = [];
}

// Fetch featured articles from API
let featuredArticles: ArticleDetailResp[] = [];
try {
  const articleData = await Api.Article.getArticleList({ featured: true, page_size: 3 });
  featuredArticles = articleData.article_list || [];
} catch (error) {
  console.error('Failed to fetch featured articles:', error);
  featuredArticles = [];
}

// Fetch recent articles from API
let recentArticles: ArticleDetailResp[] = [];
try {
  const articleData = await Api.Article.getArticleList({ page: 1, page_size: 6, sort_by: 'publish_date', sort_direction: 'desc' });
  recentArticles = articleData.article_list || [];
} catch (error) {
  console.error('Failed to fetch recent articles:', error);
  recentArticles = [];
}

// Fetch popular brands from API
let popularBrands: BrandDetailResp[] = [];
try {
  const brandData = await Api.Brand.getBrandList({ featured: true, page_size: 6 });
  popularBrands = brandData.brand_list || [];
} catch (error) {
  console.error('Failed to fetch popular brands:', error);
  popularBrands = [];
}

// Fetch deals from API - will be passed to DealsSection component
let deals: DealDetailResp[] = [];
try {
  const dealData = await Api.Deal.getDealList({ page: 1, page_size: 12, sort_by: 'end_date', sort_direction: 'asc' });
  deals = dealData.deal_list || [];
} catch (error) {
  console.error('Failed to fetch deals:', error);
  deals = [];
}
---

<MainLayout 
  title="SmartReviews - Insightful Articles & Exclusive Deals"
  description="Discover insightful articles, helpful guides, and exclusive deals on the latest tech products and consumer goods."
>
  <!-- Viewport section with Hero component -->
  <section class="viewport-section relative overflow-hidden" style="margin-top: -2px;">
    <Hero articles={recentArticles.slice(0, 6)} />
  </section>
  
  <!-- Enhanced Featured Articles Carousel with Dynamic Background -->
  <section class="py-20 relative overflow-hidden ghibli-dynamic-bg ghibli-particles-enhanced" data-section="featured-carousel">
    <div class="absolute inset-0 ghibli-clouds opacity-30"></div>
    <div class="absolute top-0 right-0 w-96 h-96 bg-gradient-radial from-ghibli-spring-200/40 to-transparent blur-3xl"></div>
    <div class="absolute bottom-0 left-0 w-80 h-80 bg-gradient-radial from-ghibli-warm-200/30 to-transparent blur-3xl"></div>

    <div class="container mx-auto px-4 relative z-10">
      <FeaturedCarousel articles={featuredArticles} />
    </div>
  </section>

  <!-- Enhanced Today's Best Deals Section with Parallax -->
  <section class="py-24 relative overflow-hidden ghibli-mouse-light" data-section="deals" style="background: linear-gradient(135deg, rgba(254, 252, 232, 0.8) 0%, rgba(255, 251, 240, 0.9) 100%);">
    <div class="ghibli-parallax-container">
      <div class="ghibli-parallax-layer ghibli-parallax-back">
        <div class="absolute inset-0 opacity-20">
          <svg class="w-full h-full" viewBox="0 0 1000 400" preserveAspectRatio="xMidYMid slice">
            <defs>
              <pattern id="dealPattern" x="0" y="0" width="100" height="100" patternUnits="userSpaceOnUse">
                <circle cx="50" cy="50" r="2" fill="rgba(228,166,114,0.3)"/>
                <circle cx="25" cy="25" r="1.5" fill="rgba(216,116,94,0.2)"/>
                <circle cx="75" cy="75" r="1" fill="rgba(177,180,121,0.25)"/>
              </pattern>
            </defs>
            <rect width="1000" height="400" fill="url(#dealPattern)"/>
          </svg>
        </div>
      </div>
      <div class="ghibli-parallax-layer ghibli-parallax-front">
        <div class="container mx-auto px-4 relative z-10">
          <DealsSection deals={deals} />
        </div>
      </div>
    </div>

    <!-- Floating decorative elements -->
    <div class="absolute top-1/4 left-10 w-8 h-8 opacity-20 ghibli-float-physics">
      <svg viewBox="0 0 24 24" fill="currentColor" class="text-ghibli-warm-400">
        <path d="M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22A10,10 0 0,1 2,12A10,10 0 0,1 12,2M12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20A8,8 0 0,0 20,12A8,8 0 0,0 12,4Z"/>
      </svg>
    </div>
    <div class="absolute bottom-1/3 right-16 w-6 h-6 opacity-25 ghibli-float-physics" style="animation-delay: -3s;">
      <svg viewBox="0 0 24 24" fill="currentColor" class="text-ghibli-sunset-400">
        <path d="M12,2L14.5,9.5H21L15.5,14L17.5,21L12,17L6.5,21L8.5,14L3,9.5H9.5L12,2Z"/>
      </svg>
    </div>
  </section>

  <!-- Enhanced Browse Categories Grid with Depth Effects -->
  <section class="py-24 relative overflow-hidden ghibli-glow-enhanced" data-section="categories" style="background: linear-gradient(135deg, rgba(248, 250, 252, 0.95) 0%, rgba(240, 244, 255, 0.9) 100%);">
    <div class="absolute inset-0 ghibli-depth-blur"></div>
    <div class="absolute top-1/3 right-1/4 w-96 h-96 bg-gradient-radial from-ghibli-earth-200/30 to-transparent blur-3xl ghibli-float-physics"></div>
    <div class="absolute bottom-1/4 left-1/3 w-80 h-80 bg-gradient-radial from-ghibli-spring-200/25 to-transparent blur-3xl ghibli-float-physics" style="animation-delay: -5s;"></div>

    <div class="container mx-auto px-4 relative z-10">
      <CategoryGrid categories={categories} />
    </div>

    <!-- Nature-inspired decorative elements -->
    <div class="absolute top-20 left-1/4 transform rotate-12 opacity-15">
      <svg width="60" height="60" viewBox="0 0 24 24" fill="none" class="text-ghibli-earth-400">
        <path d="M17,8C8,10 5.9,16.17 3.82,21.34L5.71,22L6.66,19.7C7.14,19.87 7.64,20 8,20C19,20 22,3 22,3C21,5 14,5.25 9,6.25C4,7.25 2,11.5 2,13.5C2,15.5 3.75,17.25 3.75,17.25C7,8 17,8 17,8Z" fill="currentColor"/>
      </svg>
    </div>
    <div class="absolute bottom-32 right-1/5 transform -rotate-6 opacity-20">
      <svg width="40" height="40" viewBox="0 0 24 24" fill="none" class="text-ghibli-spring-400">
        <path d="M12,2A3,3 0 0,1 15,5V11A3,3 0 0,1 12,14A3,3 0 0,1 9,11V5A3,3 0 0,1 12,2M19,11C19,14.53 16.39,17.44 13,17.93V21H11V17.93C7.61,17.44 5,14.53 5,11H7A5,5 0 0,0 12,16A5,5 0 0,0 17,11H19Z" fill="currentColor"/>
      </svg>
    </div>
  </section>

  <!-- Enhanced Popular Tags with Fireflies Effect -->
  <section class="py-20 relative overflow-hidden ghibli-fireflies" data-section="tags" style="background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(254, 252, 232, 0.9) 100%);">
    <div class="absolute inset-0 ghibli-wind"></div>
    <div class="container mx-auto px-4 relative z-10">
      <TagList tags={popularTags} title="Popular Tags" showCount={true} className="max-w-4xl mx-auto" />
    </div>

    <!-- Subtle pattern overlay -->
    <div class="absolute inset-0 opacity-5">
      <svg class="w-full h-full" viewBox="0 0 400 400" preserveAspectRatio="xMidYMid slice">
        <defs>
          <pattern id="tagPattern" x="0" y="0" width="50" height="50" patternUnits="userSpaceOnUse">
            <path d="M25,5 L45,25 L25,45 L5,25 Z" fill="rgba(142,155,94,0.1)" stroke="none"/>
          </pattern>
        </defs>
        <rect width="400" height="400" fill="url(#tagPattern)"/>
      </svg>
    </div>
  </section>

  <!-- Enhanced Newsletter Subscription with Dynamic Effects -->
  <section class="relative overflow-hidden ghibli-dynamic-bg ghibli-particles-enhanced" data-section="newsletter">
    <div class="absolute inset-0 ghibli-landscape"></div>
    <Newsletter />
  </section>
  
  <!-- Enhanced Featured Articles Section with Advanced Visual Effects -->
  <section class="py-24 relative overflow-hidden ghibli-dynamic-bg ghibli-particles-enhanced ghibli-mouse-light" data-section="featured-articles" style="background: linear-gradient(135deg, rgba(240, 244, 255, 0.9) 0%, rgba(232, 241, 255, 0.85) 50%, rgba(248, 250, 252, 0.9) 100%);">
    <!-- Multi-layered background effects -->
    <div class="absolute inset-0 ghibli-watercolor"></div>
    <div class="absolute top-0 right-0 w-[500px] h-[500px] bg-gradient-radial from-ghibli-spring-200/30 to-transparent blur-3xl ghibli-float-physics"></div>
    <div class="absolute bottom-0 left-0 w-[400px] h-[400px] bg-gradient-radial from-ghibli-warm-200/25 to-transparent blur-3xl ghibli-float-physics" style="animation-delay: -4s;"></div>
    <div class="absolute top-1/3 left-1/4 w-[300px] h-[300px] bg-gradient-radial from-ghibli-earth-200/20 to-transparent blur-3xl ghibli-float-physics" style="animation-delay: -8s;"></div>

    <!-- Enhanced floating decorative elements -->
    <div class="absolute top-16 right-1/4 transform rotate-12 opacity-15 ghibli-float-physics">
      <svg width="80" height="80" viewBox="0 0 100 100" fill="none" class="text-ghibli-spring-400">
        <circle cx="50" cy="50" r="40" fill="currentColor" opacity="0.3"/>
        <circle cx="50" cy="50" r="25" fill="currentColor" opacity="0.5"/>
        <circle cx="50" cy="50" r="10" fill="currentColor" opacity="0.7"/>
      </svg>
    </div>
    <div class="absolute bottom-20 left-1/5 transform -rotate-6 opacity-20 ghibli-float-physics" style="animation-delay: -6s;">
      <svg width="60" height="60" viewBox="0 0 100 100" fill="none" class="text-ghibli-warm-400">
        <path d="M50,10 L60,40 L90,50 L60,60 L50,90 L40,60 L10,50 L40,40 Z" fill="currentColor" opacity="0.6"/>
      </svg>
    </div>
    <div class="absolute top-1/2 right-10 transform rotate-45 opacity-10 ghibli-float-physics" style="animation-delay: -2s;">
      <svg width="50" height="50" viewBox="0 0 100 100" fill="none" class="text-ghibli-earth-400">
        <polygon points="50,5 61,35 91,35 68,57 79,91 50,70 21,91 32,57 9,35 39,35" fill="currentColor" opacity="0.8"/>
      </svg>
    </div>

    <!-- Parallax layers for depth -->
    <div class="ghibli-parallax-container">
      <div class="ghibli-parallax-layer ghibli-parallax-back">
        <div class="absolute inset-0 opacity-10">
          <svg class="w-full h-full" viewBox="0 0 1000 600" preserveAspectRatio="xMidYMid slice">
            <defs>
              <pattern id="articlePattern" x="0" y="0" width="200" height="200" patternUnits="userSpaceOnUse">
                <circle cx="100" cy="100" r="3" fill="rgba(142,155,94,0.2)"/>
                <circle cx="50" cy="50" r="2" fill="rgba(228,166,114,0.15)"/>
                <circle cx="150" cy="150" r="2.5" fill="rgba(216,116,94,0.18)"/>
                <circle cx="25" cy="175" r="1.5" fill="rgba(177,180,121,0.12)"/>
                <circle cx="175" cy="25" r="1.8" fill="rgba(134,115,87,0.14)"/>
              </pattern>
            </defs>
            <rect width="1000" height="600" fill="url(#articlePattern)"/>
          </svg>
        </div>
      </div>
    </div>
    
    <div class={`container mx-auto px-4 ${containerWidth} relative z-10`}>
      <div class="flex flex-col sm:flex-row justify-between items-center mb-16">
        <div class="relative">
          <span class="inline-block px-5 py-2 text-sm font-medium text-ghibli-forest-800 bg-gradient-to-r from-ghibli-spring-100 to-ghibli-spring-200 rounded-full mb-4 shadow-md">Curated Collection</span>
          <h2 class="text-3xl md:text-5xl font-ghibliHeading font-bold text-ghibli-forest-800 relative inline-block">
            Featured Articles
            <span class="absolute -bottom-3 left-0 h-1.5 w-24 bg-gradient-to-r from-ghibli-warm-400 to-ghibli-warm-500 rounded-full"></span>
          </h2>
          <!-- Subtle decorative element -->
          <div class="absolute -right-10 -top-6 opacity-20">
            <svg width="60" height="60" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="text-ghibli-warm-400">
              <path d="M6.5 6.5C7.5 5.5 9 5 12 5C15 5 16.5 5.5 17.5 6.5C18.5 7.5 19 9 19 12C19 15 18.5 16.5 17.5 17.5C16.5 18.5 15 19 12 19C9 19 7.5 18.5 6.5 17.5C5.5 16.5 5 15 5 12C5 9 5.5 7.5 6.5 6.5Z" stroke="currentColor" stroke-width="1" fill="none"/>
            </svg>
          </div>
        </div>
        <a href="/articles" class="mt-4 sm:mt-0 group text-ghibli-warm-600 hover:text-ghibli-warm-700 font-medium flex items-center transition-all duration-300 hover:translate-x-1 bg-white/50 backdrop-blur-sm px-5 py-2 rounded-full shadow-sm">
          View all
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-1.5 transform group-hover:translate-x-1 transition-transform" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
          </svg>
        </a>
      </div>
      
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 lg:gap-10">
        {featuredArticles.map((article, index) => (
          <article class="bg-white rounded-3xl shadow-ghibli overflow-hidden border border-ghibli-earth-100/60 h-full flex flex-col hover:shadow-ghibli-hover transform hover:-translate-y-2 transition-all duration-500 group relative">
            <!-- Subtle glow effect on hover -->
            <div class="absolute -inset-0.5 bg-gradient-to-r from-ghibli-spring-400/20 to-ghibli-warm-400/20 rounded-3xl opacity-0 group-hover:opacity-100 blur-md transition-all duration-700"></div>
            
            <div class="relative pb-[60%] overflow-hidden">
              <img 
                src={article.cover_image || `/images/placeholder-${(index % 3) + 1}.jpg`} 
                alt={article.title}
                class="absolute inset-0 w-full h-full object-cover transition-transform duration-1000 group-hover:scale-105"
              />
              <div class="absolute inset-0 bg-gradient-to-t from-ghibli-earth-900/60 to-transparent"></div>
              <span class="absolute top-4 left-4 bg-gradient-to-r from-ghibli-forest-600 to-ghibli-forest-700 text-white text-sm font-medium px-4 py-1.5 rounded-full shadow-md">
                {article.category?.name || "Uncategorized"}
              </span>
              <!-- Reading time badge -->
              <span class="absolute top-4 right-4 bg-black/30 backdrop-blur-md text-white text-xs px-3 py-1 rounded-full">
                {article.reading_time || 5} min read
              </span>
            </div>
            
            <div class="p-7 md:p-8 flex-grow flex flex-col relative">
              <!-- Decorative element -->
              <div class="absolute top-3 right-3 opacity-10">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="text-ghibli-warm-500">
                  <path d="M12 2L14.5 9.5H21L15.5 14L17.5 21L12 17L6.5 21L8.5 14L3 9.5H9.5L12 2Z" fill="currentColor"/>
                </svg>
              </div>
              
              <h3 class="text-xl font-ghibliHeading font-bold mb-3 line-clamp-2">
                <a href={`/article/${article.slug}`} class="text-ghibli-forest-800 hover:text-ghibli-warm-600 transition-colors">
                  {article.title}
                </a>
              </h3>
              <p class="text-ghibli-earth-600 mb-6 flex-grow">{article.excerpt || article.description}</p>
              <div class="flex justify-between items-center text-sm mt-4 pt-4 border-t border-ghibli-earth-100">
                <span class="text-ghibli-earth-500 flex items-center">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                  </svg>
                  {new Date(article.publish_date).toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' })}
                </span>
                <span class="flex items-center">
                  <img src={article.author?.avatar || `https://ui-avatars.com/api/?name=${encodeURIComponent(article.author?.name || 'Unknown')}&background=8e9b5e&color=fff`} 
                       alt={article.author?.name || "Author"} 
                       class="w-7 h-7 rounded-full mr-2 border-2 border-ghibli-spring-200" />
                  <span class="text-ghibli-earth-700 font-medium">{article.author?.name || "Unknown Author"}</span>
                </span>
              </div>
            </div>
          </article>
        ))}
      </div>
    </div>
  </section>
  
  <!-- Enhanced Popular Brands Section with Interactive Elements -->
  <section class="py-24 relative overflow-hidden ghibli-glow-enhanced ghibli-mouse-light" data-section="brands" style="background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(254, 252, 232, 0.9) 50%, rgba(255, 251, 240, 0.95) 100%);">
    <!-- Dynamic background layers -->
    <div class="absolute inset-0 ghibli-depth-blur"></div>
    <div class="absolute top-0 right-0 w-[450px] h-[450px] bg-gradient-radial from-ghibli-spring-200/25 to-transparent blur-3xl ghibli-float-physics"></div>
    <div class="absolute bottom-0 left-0 w-[400px] h-[400px] bg-gradient-radial from-ghibli-warm-200/20 to-transparent blur-3xl ghibli-float-physics" style="animation-delay: -7s;"></div>
    <div class="absolute top-1/2 right-1/3 w-[300px] h-[300px] bg-gradient-radial from-ghibli-earth-200/15 to-transparent blur-3xl ghibli-float-physics" style="animation-delay: -3s;"></div>

    <!-- Enhanced decorative pattern -->
    <div class="absolute inset-0 opacity-8">
      <svg class="w-full h-full" viewBox="0 0 800 600" preserveAspectRatio="xMidYMid slice">
        <defs>
          <pattern id="brandPattern" x="0" y="0" width="120" height="120" patternUnits="userSpaceOnUse">
            <g opacity="0.1">
              <circle cx="60" cy="60" r="4" fill="rgba(142,155,94,0.3)"/>
              <circle cx="30" cy="30" r="2.5" fill="rgba(228,166,114,0.25)"/>
              <circle cx="90" cy="90" r="3" fill="rgba(216,116,94,0.2)"/>
              <rect x="20" y="50" width="8" height="8" rx="2" fill="rgba(177,180,121,0.15)" transform="rotate(45 24 54)"/>
              <rect x="70" y="20" width="6" height="6" rx="1" fill="rgba(134,115,87,0.18)" transform="rotate(30 73 23)"/>
            </g>
          </pattern>
        </defs>
        <rect width="800" height="600" fill="url(#brandPattern)"/>
      </svg>
    </div>

    <!-- Floating brand-themed decorations -->
    <div class="absolute top-1/4 left-12 opacity-12 ghibli-float-physics">
      <svg width="70" height="70" viewBox="0 0 100 100" fill="none" class="text-ghibli-spring-400">
        <rect x="20" y="20" width="60" height="60" rx="8" fill="currentColor" opacity="0.3"/>
        <rect x="30" y="30" width="40" height="40" rx="4" fill="currentColor" opacity="0.5"/>
        <circle cx="50" cy="50" r="8" fill="currentColor" opacity="0.7"/>
      </svg>
    </div>
    <div class="absolute bottom-1/3 right-20 opacity-15 ghibli-float-physics" style="animation-delay: -5s;">
      <svg width="50" height="50" viewBox="0 0 100 100" fill="none" class="text-ghibli-warm-400">
        <path d="M50,10 Q80,30 90,50 Q80,70 50,90 Q20,70 10,50 Q20,30 50,10 Z" fill="currentColor" opacity="0.6"/>
      </svg>
    </div>
    
    <div class={`container mx-auto px-4 ${containerWidth} relative z-10`}>
      <div class="flex justify-between items-center mb-12">
        <div class="relative">
          <h2 class="text-3xl md:text-4xl font-ghibliHeading font-bold text-ghibli-forest-800 relative inline-block">
            Popular Brands
            <span class="absolute -bottom-3 left-0 h-1.5 w-20 bg-gradient-to-r from-ghibli-warm-400 to-ghibli-warm-500 rounded-full"></span>
          </h2>
          <!-- Subtle decorative element -->
          <div class="absolute -left-8 -top-6 opacity-20">
            <svg width="40" height="40" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="text-ghibli-spring-400">
              <path d="M6.5 6.5C7.5 5.5 9 5 12 5C15 5 16.5 5.5 17.5 6.5C18.5 7.5 19 9 19 12C19 15 18.5 16.5 17.5 17.5C16.5 18.5 15 19 12 19C9 19 7.5 18.5 6.5 17.5C5.5 16.5 5 15 5 12C5 9 5.5 7.5 6.5 6.5Z" stroke="currentColor" stroke-width="1" fill="none"/>
            </svg>
          </div>
        </div>
        <a href="/brands" class="text-ghibli-warm-600 hover:text-ghibli-warm-700 font-medium flex items-center group bg-white/50 backdrop-blur-sm px-5 py-2 rounded-full shadow-sm">
          View all
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-1.5 transform group-hover:translate-x-1 transition-transform" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
          </svg>
        </a>
      </div>
      
      <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-6">
        {popularBrands.map(brand => (
          <a href={`/brand/${brand.slug}`} class="bg-white rounded-2xl shadow-ghibli p-6 flex items-center justify-center hover:shadow-ghibli-hover transition-all duration-300 hover:-translate-y-1 border border-ghibli-earth-100/60 group relative">
            <!-- Subtle glow effect on hover -->
            <div class="absolute -inset-0.5 bg-gradient-to-r from-ghibli-spring-400/20 to-ghibli-warm-400/20 rounded-2xl opacity-0 group-hover:opacity-100 blur-md transition-all duration-700"></div>
            <img 
              src={brand.logo || `/images/brand-placeholder.svg`} 
              alt={`${brand.name} logo`}
              class="h-12 max-w-full object-contain relative z-10 transition-transform duration-300 group-hover:scale-110"
            />
          </a>
        ))}
      </div>
    </div>
  </section>
  
  <!-- Enhanced Recent Articles Section with Layered Effects -->
  <section class="py-24 relative overflow-hidden ghibli-particles-enhanced ghibli-wind" data-section="recent-articles" style="background: linear-gradient(135deg, rgba(248, 250, 252, 0.95) 0%, rgba(240, 244, 255, 0.9) 50%, rgba(255, 255, 255, 0.95) 100%);">
    <!-- Multi-layered atmospheric effects -->
    <div class="absolute inset-0 ghibli-fireflies opacity-30"></div>
    <div class="absolute top-1/4 right-1/4 w-[420px] h-[420px] bg-gradient-radial from-ghibli-spring-200/18 to-transparent blur-3xl ghibli-float-physics"></div>
    <div class="absolute bottom-1/4 left-1/4 w-[380px] h-[380px] bg-gradient-radial from-ghibli-warm-200/15 to-transparent blur-3xl ghibli-float-physics" style="animation-delay: -6s;"></div>
    <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[350px] h-[350px] bg-gradient-radial from-ghibli-earth-200/12 to-transparent blur-3xl ghibli-float-physics" style="animation-delay: -9s;"></div>

    <!-- Enhanced decorative elements with reading theme -->
    <div class="absolute top-16 left-1/5 opacity-10 ghibli-float-physics">
      <svg width="90" height="90" viewBox="0 0 100 100" fill="none" class="text-ghibli-earth-400">
        <rect x="20" y="15" width="60" height="70" rx="4" fill="currentColor" opacity="0.4"/>
        <rect x="25" y="20" width="50" height="60" rx="2" fill="currentColor" opacity="0.6"/>
        <line x1="30" y1="30" x2="65" y2="30" stroke="currentColor" stroke-width="2" opacity="0.3"/>
        <line x1="30" y1="40" x2="65" y2="40" stroke="currentColor" stroke-width="2" opacity="0.3"/>
        <line x1="30" y1="50" x2="55" y2="50" stroke="currentColor" stroke-width="2" opacity="0.3"/>
      </svg>
    </div>
    <div class="absolute bottom-20 right-1/6 opacity-12 ghibli-float-physics" style="animation-delay: -4s;">
      <svg width="70" height="70" viewBox="0 0 100 100" fill="none" class="text-ghibli-spring-400">
        <circle cx="50" cy="50" r="35" fill="currentColor" opacity="0.2"/>
        <circle cx="50" cy="50" r="25" fill="currentColor" opacity="0.3"/>
        <circle cx="50" cy="50" r="15" fill="currentColor" opacity="0.4"/>
        <circle cx="50" cy="50" r="8" fill="currentColor" opacity="0.5"/>
      </svg>
    </div>
    <div class="absolute top-1/3 right-12 opacity-8 ghibli-float-physics" style="animation-delay: -7s;">
      <svg width="60" height="60" viewBox="0 0 100 100" fill="none" class="text-ghibli-warm-400">
        <path d="M30,20 Q50,10 70,20 Q80,40 70,60 Q50,70 30,60 Q20,40 30,20 Z" fill="currentColor" opacity="0.5"/>
        <path d="M40,30 Q50,25 60,30 Q65,40 60,50 Q50,55 40,50 Q35,40 40,30 Z" fill="currentColor" opacity="0.7"/>
      </svg>
    </div>

    <!-- Subtle texture overlay -->
    <div class="absolute inset-0 opacity-5">
      <svg class="w-full h-full" viewBox="0 0 600 400" preserveAspectRatio="xMidYMid slice">
        <defs>
          <pattern id="recentPattern" x="0" y="0" width="80" height="80" patternUnits="userSpaceOnUse">
            <g opacity="0.15">
              <path d="M20,20 L60,20 L60,60 L20,60 Z" fill="none" stroke="rgba(142,155,94,0.2)" stroke-width="0.5"/>
              <circle cx="40" cy="40" r="8" fill="rgba(228,166,114,0.1)"/>
              <path d="M10,40 Q40,20 70,40" fill="none" stroke="rgba(216,116,94,0.15)" stroke-width="0.8"/>
            </g>
          </pattern>
        </defs>
        <rect width="600" height="400" fill="url(#recentPattern)"/>
      </svg>
    </div>
    
    <div class={`container mx-auto px-4 ${containerWidth} relative z-10`}>
      <div class="flex flex-col sm:flex-row justify-between items-center mb-16">
        <div class="relative">
          <span class="inline-block px-5 py-2 text-sm font-medium text-ghibli-forest-800 bg-gradient-to-r from-ghibli-spring-100 to-ghibli-spring-200 rounded-full mb-4 shadow-md">Latest Updates</span>
          <h2 class="text-3xl md:text-5xl font-ghibliHeading font-bold text-ghibli-forest-800 relative inline-block">
            Recent Articles
            <span class="absolute -bottom-3 left-0 h-1.5 w-24 bg-gradient-to-r from-ghibli-warm-400 to-ghibli-warm-500 rounded-full"></span>
          </h2>
          <!-- Subtle decorative element -->
          <div class="absolute -left-10 -top-6 opacity-20">
            <svg width="60" height="60" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="text-ghibli-spring-400">
              <path d="M6.5 6.5C7.5 5.5 9 5 12 5C15 5 16.5 5.5 17.5 6.5C18.5 7.5 19 9 19 12C19 15 18.5 16.5 17.5 17.5C16.5 18.5 15 19 12 19C9 19 7.5 18.5 6.5 17.5C5.5 16.5 5 15 5 12C5 9 5.5 7.5 6.5 6.5Z" stroke="currentColor" stroke-width="1" fill="none"/>
            </svg>
          </div>
        </div>
        <a href="/articles" class="mt-4 sm:mt-0 group text-ghibli-warm-600 hover:text-ghibli-warm-700 font-medium flex items-center transition-all duration-300 hover:translate-x-1 bg-white/50 backdrop-blur-sm px-5 py-2 rounded-full shadow-sm">
          View all
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-1.5 transform group-hover:translate-x-1 transition-transform" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
          </svg>
        </a>
      </div>
      
      <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
        {recentArticles.map((article, index) => (
          <article class="flex border-b border-ghibli-earth-100 pb-8 last:border-0 last:pb-0 group">
            <div class="w-24 h-24 sm:w-32 sm:h-32 flex-shrink-0 rounded-2xl overflow-hidden border-2 border-ghibli-earth-100/60 shadow-sm group-hover:shadow-md transition-all duration-300 relative">
              <img 
                src={article.cover_image || `/images/placeholder-${(index % 3) + 1}.jpg`} 
                alt={article.title}
                class="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110"
              />
              <!-- Subtle overlay -->
              <div class="absolute inset-0 bg-gradient-to-t from-ghibli-earth-900/30 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            </div>
            <div class="ml-6 flex-1">
              <div class="flex items-center mb-2">
                <span class="text-xs font-medium px-3 py-1 bg-gradient-to-r from-ghibli-spring-100 to-ghibli-spring-200 text-ghibli-forest-800 rounded-full shadow-sm">{article.category?.name || "Uncategorized"}</span>
                <span class="ml-auto text-xs text-ghibli-earth-500 flex items-center">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-3.5 w-3.5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                  </svg>
                  {new Date(article.publish_date).toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' })}
                </span>
              </div>
              <h3 class="text-lg sm:text-xl font-ghibliHeading font-bold mb-2">
                <a href={`/article/${article.slug}`} class="text-ghibli-forest-800 hover:text-ghibli-warm-600 transition-colors">
                  {article.title}
                </a>
              </h3>
              <p class="text-sm text-ghibli-earth-600 line-clamp-2 mb-3">{article.excerpt || article.description}</p>
              <div class="flex items-center">
                <img 
                  src={article.author?.avatar || `https://ui-avatars.com/api/?name=${encodeURIComponent(article.author?.name || 'Unknown')}&background=8e9b5e&color=fff`}
                  alt={article.author?.name || "Author"} 
                  class="w-6 h-6 rounded-full border-2 border-ghibli-spring-200 mr-2" 
                />
                <span class="text-sm text-ghibli-earth-700">{article.author?.name || "Unknown Author"}</span>
                <span class="ml-auto text-ghibli-warm-600 text-sm font-medium group-hover:translate-x-1 transition-transform duration-300 flex items-center">
                  Read more
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                  </svg>
                </span>
              </div>
            </div>
          </article>
        ))}
      </div>
    </div>
  </section>
  
  <!-- Newsletter Section -->
  <Newsletter />
</MainLayout>

<style>
  /* Enhanced Viewport Section */
  .viewport-section {
    height: 100vh;
    min-height: 100vh;
    max-height: 100vh;
    overflow: hidden;
    padding: 0;
    margin: 0;
    margin-top: -2px;
    position: relative;
  }

  /* Section-specific enhancements */
  section[data-section] {
    position: relative;
    transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
  }

  /* Scroll-triggered animations */
  section[data-section].animate-in {
    opacity: 1;
    transform: translateY(0);
  }

  section[data-section]:not(.animate-in) {
    opacity: 0.8;
    transform: translateY(20px);
  }

  /* Enhanced gradient backgrounds */
  .bg-gradient-radial {
    background: radial-gradient(circle, var(--tw-gradient-stops));
  }

  /* Smooth scroll behavior */
  html {
    scroll-behavior: smooth;
  }

  /* Performance optimizations */
  section[data-section] * {
    will-change: transform;
  }

  /* Hover effects for interactive elements */
  section[data-section]:hover .ghibli-float-physics {
    animation-play-state: paused;
  }

  /* Custom scrollbar */
  ::-webkit-scrollbar {
    width: 8px;
  }

  ::-webkit-scrollbar-track {
    background: rgba(240, 244, 255, 0.3);
  }

  ::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, rgba(142, 155, 94, 0.6), rgba(228, 166, 114, 0.6));
    border-radius: 4px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, rgba(142, 155, 94, 0.8), rgba(228, 166, 114, 0.8));
  }

  /* Responsive adjustments */
  @media (max-width: 768px) {
    section[data-section] {
      padding-left: 1rem;
      padding-right: 1rem;
    }

    .ghibli-float-physics {
      animation-duration: 8s;
    }
  }

  @media (prefers-reduced-motion: reduce) {
    .ghibli-float-physics,
    .ghibli-particles-enhanced,
    .ghibli-dynamic-bg {
      animation: none;
    }

    section[data-section] {
      transition: none;
    }
  }
</style>

<script>
  // Enhanced page-level interactions
  document.addEventListener('DOMContentLoaded', () => {
    // Intersection Observer for scroll animations
    const observerOptions = {
      threshold: 0.15,
      rootMargin: '0px 0px -100px 0px'
    };

    const sectionObserver = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          entry.target.classList.add('animate-in');

          // Trigger specific animations based on section
          const section = entry.target.getAttribute('data-section');
          if (section) {
            triggerSectionAnimation(section, entry.target);
          }
        }
      });
    }, observerOptions);

    // Observe all sections
    const sections = document.querySelectorAll('section[data-section]');
    sections.forEach(section => sectionObserver.observe(section));

    // Section-specific animations
    function triggerSectionAnimation(sectionName, element) {
      switch(sectionName) {
        case 'featured-carousel':
          animateCarousel(element);
          break;
        case 'deals':
          animateDeals(element);
          break;
        case 'categories':
          animateCategories(element);
          break;
        case 'tags':
          animateTags(element);
          break;
        case 'featured-articles':
          animateFeaturedArticles(element);
          break;
        case 'brands':
          animateBrands(element);
          break;
        case 'recent-articles':
          animateRecentArticles(element);
          break;
      }
    }

    // Animation functions for each section
    function animateCarousel(element) {
      const items = element.querySelectorAll('.carousel-item, .featured-item');
      items.forEach((item, index) => {
        setTimeout(() => {
          item.style.opacity = '1';
          item.style.transform = 'translateY(0) scale(1)';
        }, index * 100);
      });
    }

    function animateDeals(element) {
      const deals = element.querySelectorAll('.deal-card, .deals-grid > *');
      deals.forEach((deal, index) => {
        setTimeout(() => {
          deal.style.opacity = '1';
          deal.style.transform = 'translateX(0) rotateY(0)';
        }, index * 80);
      });
    }

    function animateCategories(element) {
      const categories = element.querySelectorAll('.category-item, .category-card');
      categories.forEach((category, index) => {
        setTimeout(() => {
          category.style.opacity = '1';
          category.style.transform = 'translateY(0) scale(1)';
        }, index * 60);
      });
    }

    function animateTags(element) {
      const tags = element.querySelectorAll('.tag-item, .tag');
      tags.forEach((tag, index) => {
        setTimeout(() => {
          tag.style.opacity = '1';
          tag.style.transform = 'translateX(0)';
        }, index * 40);
      });
    }

    function animateFeaturedArticles(element) {
      const articles = element.querySelectorAll('article, .article-card');
      articles.forEach((article, index) => {
        setTimeout(() => {
          article.style.opacity = '1';
          article.style.transform = 'translateY(0) rotateX(0)';
        }, index * 120);
      });
    }

    function animateBrands(element) {
      const brands = element.querySelectorAll('.brand-item, .brand-card');
      brands.forEach((brand, index) => {
        setTimeout(() => {
          brand.style.opacity = '1';
          brand.style.transform = 'translateY(0) scale(1)';
        }, index * 50);
      });
    }

    function animateRecentArticles(element) {
      const articles = element.querySelectorAll('article, .article-item');
      articles.forEach((article, index) => {
        setTimeout(() => {
          article.style.opacity = '1';
          article.style.transform = 'translateX(0)';
        }, index * 100);
      });
    }

    // Mouse tracking for enhanced glow effects
    let mouseX = 0;
    let mouseY = 0;

    document.addEventListener('mousemove', (e) => {
      mouseX = e.clientX;
      mouseY = e.clientY;

      // Update CSS custom properties for mouse-following effects
      document.documentElement.style.setProperty('--mouse-x', `${(mouseX / window.innerWidth) * 100}%`);
      document.documentElement.style.setProperty('--mouse-y', `${(mouseY / window.innerHeight) * 100}%`);
    });

    // Parallax scrolling effect
    let ticking = false;

    function updateParallax() {
      const scrolled = window.pageYOffset;
      const parallaxElements = document.querySelectorAll('.ghibli-parallax-layer');

      parallaxElements.forEach(element => {
        const speed = element.dataset.speed || 0.5;
        const yPos = -(scrolled * speed);
        element.style.transform = `translateY(${yPos}px)`;
      });

      ticking = false;
    }

    window.addEventListener('scroll', () => {
      if (!ticking) {
        requestAnimationFrame(updateParallax);
        ticking = true;
      }
    });

    // Performance monitoring
    if ('requestIdleCallback' in window) {
      requestIdleCallback(() => {
        console.log('Enhanced Ghibli effects loaded successfully');
      });
    }
  });
</script>