---
import MainLayout from '../layouts/MainLayout.astro';
import { siteConfig } from '../config/site';
import Api, { DealDetailResp, CategoryDetailResp } from '../lib/api';

const { containerWidth } = siteConfig;

// Get URL parameters for filtering and pagination
const url = Astro.url;
const selectedCategory = url.searchParams.get('category') || '';
const searchQuery = url.searchParams.get('search') || '';
const currentPage = parseInt(url.searchParams.get('page') || '1');
const itemsPerPage = 12;

// Fetch deals from API
let allDeals: DealDetailResp[] = [];
let totalDeals = 0;
let totalPages = 1;

try {
  // Build API request parameters
  const apiParams: any = {
    page: currentPage,
    page_size: itemsPerPage,
    active: true
  };

  if (selectedCategory) apiParams.category = selectedCategory;
  if (searchQuery) apiParams.search = searchQuery;

  // Fetch deals from API
  const dealData = await Api.Deal.getDealList(apiParams);
  allDeals = dealData.deal_list || [];
  totalDeals = dealData.total || 0;
  totalPages = Math.ceil(totalDeals / itemsPerPage);

} catch (error) {
  console.error('Failed to fetch deals:', error);
  allDeals = [];
}

// Fetch categories for filter
let allCategories: CategoryDetailResp[] = [];
try {
  const categoryData = await Api.Category.getCategoryList({ page_size: 50 });
  allCategories = categoryData.category_list || [];
} catch (error) {
  console.error('Failed to fetch categories:', error);
  allCategories = [];
}

// Helper function to generate URL with params
function getFilterUrl(params: Record<string, string> = {}) {
  const newParams = new URLSearchParams();

  // Add existing params except page (we reset to page 1 when changing filters)
  if (selectedCategory && !('category' in params)) newParams.set('category', selectedCategory);
  if (searchQuery && !('search' in params)) newParams.set('search', searchQuery);

  // Add new params
  Object.entries(params).forEach(([key, value]) => {
    if (value) newParams.set(key, value);
  });

  return `${Astro.url.pathname}${newParams.toString() ? `?${newParams.toString()}` : ''}`;
}

// Helper function for pagination URLs
function getPaginationUrl(page: number) {
  const params = new URLSearchParams();
  if (selectedCategory) params.set('category', selectedCategory);
  if (searchQuery) params.set('search', searchQuery);
  if (page > 1) params.set('page', page.toString());
  return `${Astro.url.pathname}${params.toString() ? `?${params.toString()}` : ''}`;
}

// Format date helper
function formatDate(dateString: string) {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  });
}
  {
    id: 2,
    deal_name: "Coursera Plus: First 14 Days Free Trial",
    discount: "14 Days Free",
    description: "Get unlimited access to 7,000+ world-class courses, hands-on projects, and job-ready certificate programs for 14 days free.",
    img: "https://img.gcb-static.com/i25o/ad/screenshot/2025/04/coursera-trial.png",
    begin_date: "2025-04-20",
    end_date: "2025-07-20",
    origin_url: "https://www.coursera.org/",
    tracking_url: "https://www.linkbux.com/track/coursera-plus-trial",
    category: "Education"
  },
  {
    id: 3,
    deal_name: "Udemy: Web Development Bootcamp 92% OFF",
    discount: "92% OFF",
    description: "Complete 2025 Web Development Bootcamp by Dr. Angela Yu now available with huge discount. Limited time offer.",
    img: "https://img.gcb-static.com/i25o/ad/screenshot/2025/04/udemy-webdev.png",
    begin_date: "2025-04-15",
    end_date: "2025-05-15",
    origin_url: "https://www.udemy.com/course/the-complete-web-development-bootcamp/",
    tracking_url: "https://www.linkbux.com/track/udemy-webdev-bootcamp",
    category: "Education"
  },
  {
    id: 4,
    deal_name: "Skillshare Premium: First Month Free",
    discount: "First Month Free",
    description: "Explore thousands of creative classes with one month of free access to Skillshare Premium. Cancel anytime before trial ends.",
    img: "https://img.gcb-static.com/i25o/ad/screenshot/2025/04/skillshare-premium.png",
    begin_date: "2025-04-01",
    end_date: "2025-06-30",
    origin_url: "https://www.skillshare.com/",
    tracking_url: "https://www.linkbux.com/track/skillshare-premium-trial",
    category: "Education"
  },
  {
    id: 5,
    deal_name: "DataCamp: 67% OFF Annual Subscription",
    discount: "67% OFF",
    description: "Learn data science and analytics skills with interactive coding challenges. Limited time discount on annual plans.",
    img: "https://img.gcb-static.com/i25o/ad/screenshot/2025/04/datacamp-offer.png",
    begin_date: "2025-04-10",
    end_date: "2025-05-10",
    origin_url: "https://www.datacamp.com/",
    tracking_url: "https://www.linkbux.com/track/datacamp-annual-discount",
    category: "Education"
  },
  {
    id: 6,
    deal_name: "LinkedIn Learning: Free Month of Premium",
    discount: "1 Month Free",
    description: "Access 16,000+ expert-led LinkedIn Learning courses with a free month of Premium. Build business, creative, and tech skills.",
    img: "https://img.gcb-static.com/i25o/ad/screenshot/2025/04/linkedin-learning.png",
    begin_date: "2025-04-05",
    end_date: "2025-07-05",
    origin_url: "https://www.linkedin.com/learning/",
    tracking_url: "https://www.linkbux.com/track/linkedin-learning-trial",
    category: "Education"
  },
  {
    id: 7,
    deal_name: "MasterClass Annual Membership: Buy 1 Get 1 Free",
    discount: "Buy 1 Get 1 Free",
    description: "Share the gift of learning with MasterClass 2-for-1 annual membership offer. Learn from the world's best instructors.",
    img: "https://img.gcb-static.com/i25o/ad/screenshot/2025/04/masterclass-bogo.png",
    begin_date: "2025-04-22",
    end_date: "2025-05-31",
    origin_url: "https://www.masterclass.com/",
    tracking_url: "https://www.linkbux.com/track/masterclass-bogo-offer",
    category: "Education"
  },
  {
    id: 8,
    deal_name: "Brilliant Premium: 20% OFF Annual Plan",
    discount: "20% OFF",
    description: "Develop problem-solving skills with interactive courses in math, science, and computer science at a special discount.",
    img: "https://img.gcb-static.com/i25o/ad/screenshot/2025/04/brilliant-premium.png",
    begin_date: "2025-04-18",
    end_date: "2025-05-18",
    origin_url: "https://brilliant.org/",
    tracking_url: "https://www.linkbux.com/track/brilliant-annual-discount",
    category: "Education"
  },
  {
    id: 9,
    deal_name: "Codecademy Pro: 40% OFF Annual Subscription",
    discount: "40% OFF",
    description: "Learn to code with interactive lessons at a significant discount. Perfect for beginners and intermediate programmers.",
    img: "https://img.gcb-static.com/i25o/ad/screenshot/2025/04/codecademy-pro-discount.png",
    begin_date: "2025-04-12",
    end_date: "2025-05-26",
    origin_url: "https://www.codecademy.com/",
    tracking_url: "https://www.linkbux.com/track/codecademy-pro-discount",
    category: "Education"
  },
  {
    id: 10,
    deal_name: "Rosetta Stone: Lifetime Access 50% OFF",
    discount: "50% OFF",
    description: "Learn a new language with lifetime access to all 25 languages at half price. Award-winning language learning software.",
    img: "https://img.gcb-static.com/i25o/ad/screenshot/2025/04/rosetta-stone.png",
    begin_date: "2025-04-25",
    end_date: "2025-06-25",
    origin_url: "https://www.rosettastone.com/",
    tracking_url: "https://www.linkbux.com/track/rosetta-stone-lifetime",
    category: "Education"
  }
];

// 计算每个交易的剩余天数
const calculateRemainingDays = (endDate: string): number => {
  const end = new Date(endDate);
  const now = new Date();
  const diffTime: number = end.getTime() - now.getTime();
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  return diffDays > 0 ? diffDays : 0;
};

// 添加剩余天数信息
const dealsWithDaysRemaining = deals.map(deal => {
  const daysRemaining = calculateRemainingDays(deal.end_date);
  return {
    ...deal,
    daysRemaining
  };
});

// 获取所有唯一的类别
const categories = [...new Set(deals.map(deal => deal.category))];

// 分页处理
const totalItems = dealsWithDaysRemaining.length;
const totalPages = Math.ceil(totalItems / itemsPerPage);
const startIndex = (currentPage - 1) * itemsPerPage;
const endIndex = Math.min(startIndex + itemsPerPage, totalItems);
const paginatedDeals = dealsWithDaysRemaining.slice(startIndex, endIndex);

// 生成分页链接数组
const paginationLinks: number[] = [];
for (let i = 1; i <= totalPages; i++) {
  paginationLinks.push(i);
}
---

<MainLayout
  title="Hot Deals & Discounts | SmartReviews"
  description="Find the best deals and discounts on education courses, learning platforms, and more. Limited-time offers from top providers."
>
  <!-- Page Header -->
  <section class="bg-gradient-to-r from-purple-700 to-purple-800 text-white py-12">
    <div class={`container mx-auto px-4 ${containerWidth} text-center`}>
      <h1 class="text-4xl md:text-5xl font-bold mb-4">Today's Hot Deals</h1>
      <p class="text-xl max-w-3xl mx-auto">Exclusive discounts on top-rated educational resources and courses. Click any deal to claim your offer.</p>
    </div>
  </section>
  
  <!-- Category Filters -->
  <section class="bg-white sticky top-16 z-10 py-4 border-b border-gray-100 shadow-sm">
    <div class={`container mx-auto px-4 ${containerWidth}`}>
      <div class="flex flex-wrap items-center gap-2">
        <span class="text-gray-700 font-medium mr-2">Filter by:</span>
        <a href="#all" class="px-3 py-1.5 bg-purple-600 text-white rounded-full text-sm font-medium">All Deals</a>
        {categories.map(category => (
          <a href={`#${category.toLowerCase().replace(/\s+/g, '-')}`} class="px-3 py-1.5 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-full text-sm font-medium transition-colors">
            {category}
          </a>
        ))}
      </div>
    </div>
  </section>
  
  <!-- Deals Grid -->
  <section class="py-12 bg-gray-50">
    <div class={`container mx-auto px-4 ${containerWidth}`}>
      <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {paginatedDeals.map(deal => (
          <a href={deal.tracking_url} target="_blank" rel="noopener" class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-all hover:scale-[1.02] border border-gray-100 block group">
            <div class="relative">
              <img src={deal.img} alt={deal.deal_name} class="w-full h-48 object-cover" />
              <div class="absolute top-3 right-3 bg-purple-500 text-white text-xs font-bold px-2 py-1 rounded-full">{deal.discount}</div>
              <div class="absolute bottom-0 left-0 bg-gradient-to-t from-black/70 to-transparent w-full h-12"></div>
              <div class="absolute bottom-2 left-3 flex space-x-2">
                <span class="inline-block px-2 py-0.5 bg-white/20 backdrop-blur-sm text-white text-xs rounded">{deal.category}</span>
                {deal.daysRemaining > 0 && deal.daysRemaining <= 30 && (
                  <span class="inline-block px-2 py-0.5 bg-orange-500/90 text-white text-xs rounded">{deal.daysRemaining} days left</span>
                )}
              </div>
            </div>
            
            <div class="p-4">
              <div class="mb-2">
                <h3 class="text-lg font-semibold text-gray-900 group-hover:text-purple-700 transition-colors line-clamp-2">{deal.deal_name}</h3>
              </div>
              
              <p class="text-sm text-gray-600 mb-3 line-clamp-3">{deal.description}</p>
              
              <div class="flex justify-between items-center">
                <span class="text-xs bg-purple-100 text-purple-800 px-2 py-0.5 rounded">
                  {new Date(deal.begin_date).toLocaleDateString('en-US', {year: 'numeric', month: 'short', day: 'numeric'})}
                </span>
              </div>
              
              <div class="mt-3 text-center">
                <span class="inline-block w-full py-2 bg-purple-600 hover:bg-purple-700 text-white rounded text-sm font-medium transition-colors">
                  Access Deal
                </span>
              </div>
            </div>
          </a>
        ))}
      </div>
      
      <!-- 分页控件 -->
      {totalPages > 1 && (
        <div class="flex justify-center mt-10">
          <nav class="inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
            <!-- 上一页按钮 -->
            <a 
              href={currentPage > 1 ? `?page=${currentPage - 1}` : '#'} 
              class={`relative inline-flex items-center px-3 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium ${currentPage === 1 ? 'text-gray-300 cursor-not-allowed' : 'text-gray-700 hover:bg-gray-50'}`}
            >
              <span class="sr-only">Previous</span>
              <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd" />
              </svg>
            </a>
            
            <!-- 页码 -->
            {paginationLinks.map(page => (
              <a
                href={`?page=${page}`}
                class={`relative inline-flex items-center px-4 py-2 border ${page === currentPage ? 'bg-purple-50 border-purple-500 text-purple-600 z-10' : 'bg-white border-gray-300 text-gray-700 hover:bg-gray-50'} text-sm font-medium`}
              >
                {page}
              </a>
            ))}
            
            <!-- 下一页按钮 -->
            <a 
              href={currentPage < totalPages ? `?page=${currentPage + 1}` : '#'} 
              class={`relative inline-flex items-center px-3 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium ${currentPage === totalPages ? 'text-gray-300 cursor-not-allowed' : 'text-gray-700 hover:bg-gray-50'}`}
            >
              <span class="sr-only">Next</span>
              <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
              </svg>
            </a>
          </nav>
        </div>
      )}
    </div>
  </section>
  
  <!-- Newsletter Section -->
  <section class="py-12 bg-purple-50">
    <div class={`container mx-auto px-4 ${containerWidth} text-center`}>
      <h2 class="text-2xl md:text-3xl font-bold mb-4">Never Miss a Deal</h2>
      <p class="text-gray-600 max-w-2xl mx-auto mb-6">Subscribe to our newsletter and be the first to know about exclusive discounts and offers on educational resources.</p>
      
      <form class="max-w-md mx-auto flex">
        <input 
          type="email" 
          placeholder="Your email address" 
          class="flex-grow px-4 py-3 rounded-l-md focus:outline-none focus:ring-2 focus:ring-purple-500 border border-gray-300 text-gray-600"
          required
        />
        <button 
          type="submit" 
          class="bg-purple-600 hover:bg-purple-700 text-white px-6 py-3 rounded-r-md font-medium transition-colors"
        >
          Subscribe
        </button>
      </form>
      
      <p class="text-sm text-gray-500 mt-4">We respect your privacy. Unsubscribe at any time.</p>
    </div>
  </section>
</MainLayout>

<script>
  // 简单的类别过滤功能
  document.addEventListener('DOMContentLoaded', () => {
    const filterLinks = document.querySelectorAll('[href^="#"]');
    
    filterLinks.forEach(link => {
      link.addEventListener('click', (e) => {
        e.preventDefault();
        
        // 移除所有活跃状态
        filterLinks.forEach(l => {
          l.classList.remove('bg-purple-600', 'text-white');
          l.classList.add('bg-gray-100', 'text-gray-700');
        });
        
        // 添加活跃状态到当前链接
        if (link) {
          link.classList.remove('bg-gray-100', 'text-gray-700');
          link.classList.add('bg-purple-600', 'text-white');
          
          // 获取类别名称
          const category = link.getAttribute('href')?.substring(1) || 'all';
          
          // 这里应该添加过滤逻辑，但由于这只是静态演示，我们只是改变UI状态
          console.log(`Filter by: ${category}`);
        }
      });
    });
  });
</script> 