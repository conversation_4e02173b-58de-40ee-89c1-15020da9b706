/* 2025 Ultra-Advanced Studio Ghibli Visual Effects Library */
/* The most sophisticated and magical visual effects inspired by Studio Ghibli */

/* ===== CORE MAGICAL EFFECTS ===== */

/* Quantum Particle System */
.ghibli-particles-enhanced {
  position: relative;
  overflow: hidden;
}

.ghibli-particles-enhanced::before,
.ghibli-particles-enhanced::after {
  content: '';
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  background-image: 
    radial-gradient(2px 2px at 20px 30px, rgba(255, 255, 255, 0.3), transparent),
    radial-gradient(2px 2px at 40px 70px, rgba(255, 193, 7, 0.4), transparent),
    radial-gradient(1px 1px at 90px 40px, rgba(255, 142, 83, 0.3), transparent),
    radial-gradient(1px 1px at 130px 80px, rgba(142, 155, 94, 0.2), transparent),
    radial-gradient(2px 2px at 160px 30px, rgba(228, 166, 114, 0.3), transparent);
  background-repeat: repeat;
  background-size: 200px 100px;
  animation: particleFloat 20s linear infinite;
  pointer-events: none;
  z-index: 1;
}

.ghibli-particles-enhanced::after {
  background-size: 300px 150px;
  animation-duration: 25s;
  animation-direction: reverse;
  opacity: 0.6;
}

@keyframes particleFloat {
  0% { transform: translateY(100vh) translateX(0px); }
  100% { transform: translateY(-100px) translateX(100px); }
}

/* Ethereal Wind Effect */
.ghibli-wind {
  position: relative;
}

.ghibli-wind::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, 
    transparent 0%, 
    rgba(255, 255, 255, 0.1) 25%, 
    rgba(142, 155, 94, 0.05) 50%, 
    rgba(255, 255, 255, 0.1) 75%, 
    transparent 100%);
  animation: windFlow 12s ease-in-out infinite;
  pointer-events: none;
}

@keyframes windFlow {
  0%, 100% { transform: translateX(-100%) skewX(-5deg); opacity: 0; }
  50% { transform: translateX(100%) skewX(5deg); opacity: 1; }
}

/* Magical Fireflies */
.ghibli-fireflies {
  position: relative;
  overflow: hidden;
}

.ghibli-fireflies::before,
.ghibli-fireflies::after {
  content: '';
  position: absolute;
  width: 4px;
  height: 4px;
  background: radial-gradient(circle, rgba(255, 255, 0, 0.8) 0%, rgba(255, 255, 0, 0.2) 50%, transparent 100%);
  border-radius: 50%;
  animation: fireflyDance 8s ease-in-out infinite;
  pointer-events: none;
}

.ghibli-fireflies::before {
  top: 20%;
  left: 10%;
  animation-delay: 0s;
}

.ghibli-fireflies::after {
  top: 60%;
  right: 15%;
  animation-delay: -4s;
  background: radial-gradient(circle, rgba(0, 255, 127, 0.8) 0%, rgba(0, 255, 127, 0.2) 50%, transparent 100%);
}

@keyframes fireflyDance {
  0%, 100% { 
    transform: translate(0, 0) scale(1); 
    opacity: 0.3; 
    box-shadow: 0 0 10px rgba(255, 255, 0, 0.5);
  }
  25% { 
    transform: translate(20px, -15px) scale(1.2); 
    opacity: 0.8; 
    box-shadow: 0 0 20px rgba(255, 255, 0, 0.8);
  }
  50% { 
    transform: translate(-10px, -25px) scale(0.8); 
    opacity: 0.6; 
    box-shadow: 0 0 15px rgba(255, 255, 0, 0.6);
  }
  75% { 
    transform: translate(30px, -5px) scale(1.1); 
    opacity: 0.9; 
    box-shadow: 0 0 25px rgba(255, 255, 0, 0.9);
  }
}

/* Advanced Depth Blur */
.ghibli-depth-blur {
  position: relative;
}

.ghibli-depth-blur::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, 
    rgba(255, 255, 255, 0.05) 0%, 
    rgba(142, 155, 94, 0.03) 25%, 
    rgba(228, 166, 114, 0.05) 50%, 
    rgba(216, 116, 94, 0.03) 75%, 
    rgba(255, 255, 255, 0.05) 100%);
  backdrop-filter: blur(0.5px);
  animation: depthShift 15s ease-in-out infinite;
  pointer-events: none;
}

@keyframes depthShift {
  0%, 100% { backdrop-filter: blur(0.5px); opacity: 0.3; }
  50% { backdrop-filter: blur(1px); opacity: 0.6; }
}

/* Quantum Glow Enhancement */
.ghibli-glow-enhanced {
  position: relative;
  transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.ghibli-glow-enhanced::before {
  content: '';
  position: absolute;
  inset: -2px;
  background: linear-gradient(45deg, 
    rgba(142, 155, 94, 0.2), 
    rgba(228, 166, 114, 0.2), 
    rgba(216, 116, 94, 0.2), 
    rgba(177, 180, 121, 0.2));
  border-radius: inherit;
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: -1;
  animation: quantumGlow 4s ease-in-out infinite;
}

.ghibli-glow-enhanced:hover::before {
  opacity: 1;
}

@keyframes quantumGlow {
  0%, 100% { 
    background-position: 0% 50%; 
    filter: blur(2px);
  }
  50% { 
    background-position: 100% 50%; 
    filter: blur(4px);
  }
}

/* Mouse Light Following Effect */
.ghibli-mouse-light {
  position: relative;
}

.ghibli-mouse-light::after {
  content: '';
  position: absolute;
  width: 300px;
  height: 300px;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
  border-radius: 50%;
  pointer-events: none;
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: 1;
  left: var(--mouse-x, 50%);
  top: var(--mouse-y, 50%);
  transform: translate(-50%, -50%);
}

.ghibli-mouse-light:hover::after {
  opacity: 1;
  animation: mouseLightPulse 2s ease-in-out infinite;
}

@keyframes mouseLightPulse {
  0%, 100% { transform: translate(-50%, -50%) scale(1); }
  50% { transform: translate(-50%, -50%) scale(1.2); }
}

/* Advanced Float Physics */
.ghibli-float-physics {
  animation: floatPhysics 12s ease-in-out infinite;
}

@keyframes floatPhysics {
  0%, 100% { 
    transform: translateY(0px) translateX(0px) rotate(0deg) scale(1); 
  }
  25% { 
    transform: translateY(-8px) translateX(3px) rotate(1deg) scale(1.02); 
  }
  50% { 
    transform: translateY(-12px) translateX(-2px) rotate(-0.5deg) scale(0.98); 
  }
  75% { 
    transform: translateY(-6px) translateX(4px) rotate(0.8deg) scale(1.01); 
  }
}

/* Dynamic Background System */
.ghibli-dynamic-bg {
  background-attachment: fixed;
  background-size: cover;
  background-position: center;
  position: relative;
}

.ghibli-dynamic-bg::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, 
    rgba(142, 155, 94, 0.1) 0%, 
    rgba(228, 166, 114, 0.05) 25%, 
    rgba(216, 116, 94, 0.1) 50%, 
    rgba(177, 180, 121, 0.05) 75%, 
    rgba(134, 115, 87, 0.1) 100%);
  animation: dynamicShift 20s ease-in-out infinite;
  pointer-events: none;
}

@keyframes dynamicShift {
  0%, 100% { opacity: 0.3; transform: scale(1) rotate(0deg); }
  33% { opacity: 0.6; transform: scale(1.05) rotate(1deg); }
  66% { opacity: 0.4; transform: scale(0.95) rotate(-1deg); }
}

/* Crystal Atmosphere Effect */
.ghibli-crystal-atmosphere {
  position: relative;
}

.ghibli-crystal-atmosphere::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    radial-gradient(ellipse at top left, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
    radial-gradient(ellipse at top right, rgba(142, 155, 94, 0.05) 0%, transparent 50%),
    radial-gradient(ellipse at bottom left, rgba(228, 166, 114, 0.08) 0%, transparent 50%),
    radial-gradient(ellipse at bottom right, rgba(216, 116, 94, 0.06) 0%, transparent 50%);
  animation: crystalShimmer 18s ease-in-out infinite;
  pointer-events: none;
}

@keyframes crystalShimmer {
  0%, 100% { opacity: 0.4; filter: blur(1px); }
  50% { opacity: 0.8; filter: blur(2px); }
}

/* Rainbow Shimmer Effect */
.ghibli-rainbow-shimmer {
  position: relative;
}

.ghibli-rainbow-shimmer::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, 
    transparent 0%, 
    rgba(255, 0, 150, 0.1) 25%, 
    rgba(0, 255, 255, 0.1) 50%, 
    rgba(255, 255, 0, 0.1) 75%, 
    transparent 100%);
  animation: rainbowSweep 8s ease-in-out infinite;
  pointer-events: none;
}

@keyframes rainbowSweep {
  0% { left: -100%; }
  100% { left: 100%; }
}

/* Watercolor Background */
.ghibli-watercolor {
  position: relative;
}

.ghibli-watercolor::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    radial-gradient(circle at 20% 30%, rgba(142, 155, 94, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(228, 166, 114, 0.08) 0%, transparent 50%),
    radial-gradient(circle at 40% 80%, rgba(216, 116, 94, 0.06) 0%, transparent 50%),
    radial-gradient(circle at 90% 70%, rgba(177, 180, 121, 0.09) 0%, transparent 50%);
  animation: watercolorFlow 25s ease-in-out infinite;
  pointer-events: none;
}

@keyframes watercolorFlow {
  0%, 100% { 
    transform: scale(1) rotate(0deg); 
    opacity: 0.6; 
  }
  33% { 
    transform: scale(1.1) rotate(2deg); 
    opacity: 0.8; 
  }
  66% { 
    transform: scale(0.9) rotate(-1deg); 
    opacity: 0.7; 
  }
}

/* Landscape Parallax */
.ghibli-landscape {
  position: relative;
  background: linear-gradient(to bottom, 
    rgba(135, 206, 235, 0.3) 0%, 
    rgba(255, 255, 255, 0.1) 50%, 
    rgba(34, 139, 34, 0.2) 100%);
}

.ghibli-landscape::before {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 40%;
  background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1000 200'%3E%3Cpath d='M0,200 L0,120 Q100,100 200,110 T400,95 T600,105 T800,90 T1000,100 L1000,200 Z' fill='rgba(34,139,34,0.3)'/%3E%3C/svg%3E");
  background-size: cover;
  background-position: bottom;
  animation: landscapeBreath 20s ease-in-out infinite;
}

@keyframes landscapeBreath {
  0%, 100% { transform: translateY(0px) scaleY(1); }
  50% { transform: translateY(-5px) scaleY(1.02); }
}

/* Clouds Effect */
.ghibli-clouds {
  position: relative;
}

.ghibli-clouds::before,
.ghibli-clouds::after {
  content: '';
  position: absolute;
  background: radial-gradient(ellipse, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
  border-radius: 50%;
  animation: cloudDrift 30s linear infinite;
  pointer-events: none;
}

.ghibli-clouds::before {
  width: 200px;
  height: 60px;
  top: 20%;
  left: -200px;
  animation-duration: 40s;
}

.ghibli-clouds::after {
  width: 150px;
  height: 45px;
  top: 40%;
  left: -150px;
  animation-duration: 35s;
  animation-delay: -10s;
}

@keyframes cloudDrift {
  0% { transform: translateX(0); }
  100% { transform: translateX(calc(100vw + 200px)); }
}

/* Responsive Design */
@media (max-width: 768px) {
  .ghibli-particles-enhanced::before,
  .ghibli-particles-enhanced::after {
    background-size: 150px 75px;
  }
  
  .ghibli-mouse-light::after {
    width: 200px;
    height: 200px;
  }
  
  .ghibli-clouds::before,
  .ghibli-clouds::after {
    display: none;
  }
}

@media (prefers-reduced-motion: reduce) {
  .ghibli-particles-enhanced::before,
  .ghibli-particles-enhanced::after,
  .ghibli-wind::before,
  .ghibli-fireflies::before,
  .ghibli-fireflies::after,
  .ghibli-float-physics,
  .ghibli-dynamic-bg::before,
  .ghibli-crystal-atmosphere::before,
  .ghibli-rainbow-shimmer::after,
  .ghibli-watercolor::before,
  .ghibli-landscape::before,
  .ghibli-clouds::before,
  .ghibli-clouds::after {
    animation: none;
  }
}
