/* Advanced Ghibli Visual Effects - Enhanced Version */

/* Enhanced floating particles with multiple layers */
@keyframes floatingParticles {
  0%, 100% {
    transform: translate(0, 0) rotate(0deg);
    opacity: 0.6;
  }
  25% {
    transform: translate(15px, -10px) rotate(90deg);
    opacity: 0.8;
  }
  50% {
    transform: translate(30px, 5px) rotate(180deg);
    opacity: 1;
  }
  75% {
    transform: translate(10px, 15px) rotate(270deg);
    opacity: 0.7;
  }
}

@keyframes floatingParticlesReverse {
  0%, 100% {
    transform: translate(0, 0) rotate(0deg) scale(1);
    opacity: 0.4;
  }
  25% {
    transform: translate(-10px, 8px) rotate(-45deg) scale(1.2);
    opacity: 0.6;
  }
  50% {
    transform: translate(-20px, -5px) rotate(-90deg) scale(0.8);
    opacity: 0.8;
  }
  75% {
    transform: translate(-5px, -12px) rotate(-135deg) scale(1.1);
    opacity: 0.5;
  }
}

.ghibli-particles-enhanced {
  position: relative;
  overflow: hidden;
}

.ghibli-particles-enhanced::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    radial-gradient(circle at 20% 30%, rgba(255,255,255,0.9) 1px, transparent 1px),
    radial-gradient(circle at 60% 70%, rgba(255,255,255,0.7) 1.5px, transparent 1.5px),
    radial-gradient(circle at 80% 20%, rgba(255,255,255,0.8) 0.8px, transparent 0.8px),
    radial-gradient(circle at 40% 80%, rgba(255,255,255,0.6) 1.2px, transparent 1.2px),
    radial-gradient(circle at 90% 50%, rgba(255,255,255,0.5) 0.6px, transparent 0.6px);
  background-size: 120px 120px, 180px 180px, 90px 90px, 150px 150px, 200px 200px;
  animation: floatingParticles 20s ease-in-out infinite;
  opacity: 0.7;
  pointer-events: none;
  z-index: 1;
}

.ghibli-particles-enhanced::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    radial-gradient(circle at 10% 60%, rgba(142,155,94,0.3) 2px, transparent 2px),
    radial-gradient(circle at 70% 30%, rgba(228,166,114,0.2) 1.5px, transparent 1.5px),
    radial-gradient(circle at 30% 90%, rgba(216,116,94,0.25) 1px, transparent 1px),
    radial-gradient(circle at 85% 80%, rgba(177,180,121,0.2) 1.8px, transparent 1.8px);
  background-size: 200px 200px, 160px 160px, 140px 140px, 220px 220px;
  animation: floatingParticlesReverse 25s ease-in-out infinite;
  opacity: 0.4;
  pointer-events: none;
  z-index: 2;
}

/* Enhanced ethereal glow with mouse interaction */
@keyframes glowPulse {
  0%, 100% {
    opacity: 0.3;
    transform: scale(1);
  }
  50% {
    opacity: 0.6;
    transform: scale(1.05);
  }
}

@keyframes glowShift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

.ghibli-glow-enhanced {
  position: relative;
  transition: all 0.3s ease;
}

.ghibli-glow-enhanced::before {
  content: '';
  position: absolute;
  top: -20px;
  left: -20px;
  right: -20px;
  bottom: -20px;
  background: linear-gradient(
    45deg,
    rgba(142,155,94,0.1) 0%,
    rgba(228,166,114,0.15) 25%,
    rgba(216,116,94,0.1) 50%,
    rgba(177,180,121,0.12) 75%,
    rgba(134,115,87,0.08) 100%
  );
  background-size: 400% 400%;
  border-radius: inherit;
  animation: glowShift 8s ease-in-out infinite;
  opacity: 0;
  pointer-events: none;
  z-index: -1;
  transition: opacity 0.3s ease;
}

.ghibli-glow-enhanced::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(
    circle at var(--mouse-x, 50%) var(--mouse-y, 50%),
    rgba(255, 255, 255, 0.3) 0%,
    rgba(255, 255, 255, 0.15) 30%,
    rgba(255, 255, 255, 0.05) 60%,
    transparent 80%
  );
  border-radius: inherit;
  animation: glowPulse 4s ease-in-out infinite;
  pointer-events: none;
  z-index: 1;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.ghibli-glow-enhanced:hover::before,
.ghibli-glow-enhanced:hover::after {
  opacity: 1;
}

/* Soft watercolor background */
.ghibli-watercolor {
  position: relative;
  overflow: hidden;
}

.ghibli-watercolor::before {
  content: '';
  position: absolute;
  inset: 0;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='800' height='600' viewBox='0 0 800 600'%3E%3Cfilter id='noise'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='0.65' numOctaves='3' stitchTiles='stitch'/%3E%3CfeColorMatrix type='matrix' values='1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 0.5 0'/%3E%3C/filter%3E%3Crect width='800' height='600' filter='url(%23noise)' opacity='0.15'/%3E%3C/svg%3E");
  opacity: 0.7;
  pointer-events: none;
  z-index: -1;
}

/* Hand-drawn border effect */
.ghibli-border {
  position: relative;
  border: none !important;
}

.ghibli-border::before {
  content: '';
  position: absolute;
  inset: 0;
  border: 2px solid;
  border-color: inherit;
  border-radius: inherit;
  mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='200' height='200' viewBox='0 0 200 200'%3E%3Cfilter id='noiseFilter'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='0.05' numOctaves='1' stitchTiles='stitch'/%3E%3CfeDisplacementMap in='SourceGraphic' scale='5'/%3E%3C/filter%3E%3Crect width='200' height='200' fill='white' filter='url(%23noiseFilter)'/%3E%3C/svg%3E");
  pointer-events: none;
}

/* Cloud layers */
.ghibli-clouds {
  position: relative;
  overflow: hidden;
}

@keyframes driftingClouds {
  from { transform: translateX(0); }
  to { transform: translateX(100%); }
}

.ghibli-clouds::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 200%;
  height: 100%;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1000 120'%3E%3Cpath d='M0,100 Q150,80 300,100 T600,100 T900,100 T1200,100 V120 H0 Z' fill='white' fill-opacity='0.6'/%3E%3C/svg%3E");
  background-size: 50% auto;
  background-repeat: repeat-x;
  animation: driftingClouds 60s linear infinite;
  pointer-events: none;
  z-index: 0;
}

.ghibli-clouds::after {
  content: '';
  position: absolute;
  top: 20px;
  left: -100%;
  width: 200%;
  height: 100%;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1000 120'%3E%3Cpath d='M0,100 Q150,70 300,100 T600,90 T900,100 T1200,80 V120 H0 Z' fill='white' fill-opacity='0.4'/%3E%3C/svg%3E");
  background-size: 70% auto;
  background-repeat: repeat-x;
  animation: driftingClouds 120s linear infinite;
  pointer-events: none;
  z-index: 0;
}

/* Tree silhouettes */
.ghibli-landscape {
  position: relative;
  overflow: hidden;
}

.ghibli-landscape::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 100px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1000 100'%3E%3Cpath d='M0,100 L0,70 L10,70 L15,50 L20,70 L30,70 L35,45 L40,70 L50,70 L55,55 L60,70 L70,70 L75,40 L80,70 L90,70 L95,60 L100,70 L110,70 L115,50 L120,70 L130,70 L135,45 L140,70 L150,70 L155,55 L160,70 L170,70 L175,40 L180,70 L190,70 L195,60 L200,70 L210,70 L215,50 L220,70 L230,70 L235,45 L240,70 L250,70 L255,55 L260,70 L270,70 L275,40 L280,70 L290,70 L295,60 L300,70 L310,70 L315,50 L320,70 L330,70 L335,45 L340,70 L350,70 L355,55 L360,70 L370,70 L375,40 L380,70 L390,70 L395,60 L400,70 L410,70 L415,50 L420,70 L430,70 L435,45 L440,70 L450,70 L455,55 L460,70 L470,70 L475,40 L480,70 L490,70 L495,60 L500,70 L510,70 L515,50 L520,70 L530,70 L535,45 L540,70 L550,70 L555,55 L560,70 L570,70 L575,40 L580,70 L590,70 L595,60 L600,70 L610,70 L615,50 L620,70 L630,70 L635,45 L640,70 L650,70 L655,55 L660,70 L670,70 L675,40 L680,70 L690,70 L695,60 L700,70 L710,70 L715,50 L720,70 L730,70 L735,45 L740,70 L750,70 L755,55 L760,70 L770,70 L775,40 L780,70 L790,70 L795,60 L800,70 L810,70 L815,50 L820,70 L830,70 L835,45 L840,70 L850,70 L855,55 L860,70 L870,70 L875,40 L880,70 L890,70 L895,60 L900,70 L910,70 L915,50 L920,70 L930,70 L935,45 L940,70 L950,70 L955,55 L960,70 L970,70 L975,40 L980,70 L990,70 L995,60 L1000,70 L1000,100 Z' fill='rgba(0,0,0,0.15)'/%3E%3C/svg%3E");
  background-size: cover;
  background-position: bottom;
  pointer-events: none;
}

/* Wind effects */
@keyframes gentleWind {
  0%, 100% { transform: skewX(0deg); }
  25% { transform: skewX(0.5deg); }
  75% { transform: skewX(-0.5deg); }
}

.ghibli-wind {
  animation: gentleWind 8s ease-in-out infinite;
  transform-origin: bottom;
}

/* Rain effect */
.ghibli-rain {
  position: relative;
  overflow: hidden;
}

@keyframes rainAnimation {
  0% { transform: translateY(-100%); }
  100% { transform: translateY(100%); }
}

.ghibli-rain::before {
  content: "";
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  background-image: 
    linear-gradient(transparent, transparent 20%, rgba(255, 255, 255, 0.3) 20%, transparent 40%),
    linear-gradient(transparent, transparent 30%, rgba(255, 255, 255, 0.3) 30%, transparent 50%),
    linear-gradient(transparent, transparent 50%, rgba(255, 255, 255, 0.3) 50%, transparent 70%);
  background-size: 
    100px 30px,
    200px 30px,
    150px 30px;
  animation: rainAnimation 0.5s linear infinite;
  opacity: 0.3;
  pointer-events: none;
}

/* Fireflies/light particles effect */
@keyframes fireflies {
  0%, 100% { opacity: 0; transform: scale(0.2); }
  50% { opacity: 1; transform: scale(1); }
}

.ghibli-fireflies {
  position: relative;
  overflow: hidden;
}

.ghibli-fireflies::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: 
    radial-gradient(circle at 10% 20%, rgba(255,255,255,0.8) 0.5px, transparent 0.5px),
    radial-gradient(circle at 30% 70%, rgba(255,255,255,0.7) 0.5px, transparent 0.5px),
    radial-gradient(circle at 50% 40%, rgba(255,255,255,0.6) 0.5px, transparent 0.5px),
    radial-gradient(circle at 70% 80%, rgba(255,255,255,0.7) 0.5px, transparent 0.5px),
    radial-gradient(circle at 90% 30%, rgba(255,255,255,0.8) 0.5px, transparent 0.5px);
  background-size: 100% 100%;
  animation: fireflies 4s ease-in-out infinite alternate;
  opacity: 0.6;
  pointer-events: none;
  mix-blend-mode: screen;
}

/* Advanced Dynamic Backgrounds */
@keyframes dynamicGradient {
  0% {
    background-position: 0% 50%;
    filter: hue-rotate(0deg);
  }
  25% {
    background-position: 100% 50%;
    filter: hue-rotate(5deg);
  }
  50% {
    background-position: 100% 100%;
    filter: hue-rotate(10deg);
  }
  75% {
    background-position: 0% 100%;
    filter: hue-rotate(5deg);
  }
  100% {
    background-position: 0% 50%;
    filter: hue-rotate(0deg);
  }
}

.ghibli-dynamic-bg {
  position: relative;
  background: linear-gradient(
    45deg,
    rgba(240, 244, 255, 0.9) 0%,
    rgba(232, 241, 255, 0.8) 25%,
    rgba(248, 250, 252, 0.9) 50%,
    rgba(254, 252, 232, 0.8) 75%,
    rgba(255, 251, 240, 0.9) 100%
  );
  background-size: 400% 400%;
  animation: dynamicGradient 30s ease-in-out infinite;
  overflow: hidden;
}

.ghibli-dynamic-bg::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    radial-gradient(circle at 20% 80%, rgba(142,155,94,0.1) 40%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(228,166,114,0.08) 40%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(216,116,94,0.06) 40%, transparent 50%);
  animation: floatingParticles 40s ease-in-out infinite;
  pointer-events: none;
  z-index: 1;
}

/* Mouse-following light effect */
.ghibli-mouse-light {
  position: relative;
  overflow: hidden;
}

.ghibli-mouse-light::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(
    600px circle at var(--mouse-x, 50%) var(--mouse-y, 50%),
    rgba(255, 255, 255, 0.1) 0%,
    rgba(255, 255, 255, 0.05) 40%,
    transparent 80%
  );
  pointer-events: none;
  z-index: 2;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.ghibli-mouse-light:hover::after {
  opacity: 1;
}

/* Parallax layers */
.ghibli-parallax-container {
  position: relative;
  overflow: hidden;
  perspective: 1px;
  height: 100vh;
}

.ghibli-parallax-layer {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  transform-style: preserve-3d;
}

.ghibli-parallax-back {
  transform: translateZ(-1px) scale(2);
}

.ghibli-parallax-mid {
  transform: translateZ(-0.5px) scale(1.5);
}

.ghibli-parallax-front {
  transform: translateZ(0);
}

/* Floating elements with physics */
@keyframes floatPhysics {
  0% {
    transform: translateY(0px) rotate(0deg);
    opacity: 0.7;
  }
  25% {
    transform: translateY(-20px) rotate(2deg);
    opacity: 0.9;
  }
  50% {
    transform: translateY(-10px) rotate(0deg);
    opacity: 1;
  }
  75% {
    transform: translateY(-30px) rotate(-2deg);
    opacity: 0.8;
  }
  100% {
    transform: translateY(0px) rotate(0deg);
    opacity: 0.7;
  }
}

.ghibli-float-physics {
  animation: floatPhysics 6s ease-in-out infinite;
  transform-origin: center bottom;
}

.ghibli-float-physics:nth-child(2n) {
  animation-delay: -2s;
  animation-duration: 8s;
}

.ghibli-float-physics:nth-child(3n) {
  animation-delay: -4s;
  animation-duration: 10s;
}

/* Depth layers with blur */
.ghibli-depth-blur {
  position: relative;
}

.ghibli-depth-blur::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: inherit;
  filter: blur(2px);
  opacity: 0.3;
  z-index: -1;
  transform: scale(1.1);
}