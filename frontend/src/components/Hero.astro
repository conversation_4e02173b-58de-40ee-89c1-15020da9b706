---
import { siteConfig } from '../config/site';
const { containerWidth } = siteConfig;

interface Article {
  id: number;
  slug: string;
  title: string;
  description: string;
  content: string;
  featured_image: string;
  published_at: string;
  category_info: string;
  deal_list: number[];
  tag_list: number[];
  brand_info: number;
  created_at: string;
  updated_at: string;
}

interface ApiResponse {
  code: number;
  message: string;
  data: {
    total: number;
    page: number;
    page_size: number;
    article_list: Article[];
  };
}

// Get API URL from environment variables or fallback to hardcoded value
const API_BASE_URL = import.meta.env.PUBLIC_API_URL || 'http://localhost:8080/api/v1';

// No mock data - all data comes from API calls

let articles: Article[] = [];

try {
  console.log('Fetching articles for Hero section...');
  // Fetch 6 articles instead of 5
  const response = await fetch(`${API_BASE_URL}/article?page=1&page_size=6&sort=latest`);
  
  if (response.ok) {
    const data: ApiResponse = await response.json();
    if (data.code === 0 && data.data.article_list.length > 0) {
      // Get all 6 articles from the API response
      articles = data.data.article_list.slice(0, 6);
      console.log(`Successfully fetched ${articles.length} articles for Hero section`);
    } else {
      console.warn('API returned success but no articles or unexpected format');
      throw new Error('No articles returned from API');
    }
  } else {
    console.error(`Failed to fetch articles: ${response.status} ${response.statusText}`);
    throw new Error(`API error: ${response.status}`);
  }
} catch (error) {
  console.error('Error fetching articles:', error);
  // Use empty array if API fails
  articles = [];
}

function formatDate(dateString: string): string {
  const options: Intl.DateTimeFormatOptions = { year: 'numeric', month: 'short', day: 'numeric' };
  return new Date(dateString).toLocaleDateString('en-US', options);
}

// Enhanced Ghibli-inspired color palette
const categoryColors = [
  'from-ghibli-forest-600 to-ghibli-forest-700 via-ghibli-spring-600',  // Forest green blend
  'from-ghibli-warm-500 to-ghibli-warm-600 via-ghibli-sunset-500',      // Warm orange blend
  'from-ghibli-sunset-500 to-ghibli-sunset-600 via-ghibli-warm-500',    // Sunset red blend
  'from-ghibli-spring-500 to-ghibli-spring-600 via-ghibli-forest-500',  // Spring green blend
  'from-ghibli-earth-600 to-ghibli-earth-700 via-ghibli-warm-500',      // Earth brown blend
  'from-ghibli-forest-500 to-ghibli-sunset-600 via-ghibli-warm-500',    // Forest-sunset blend
];

// Time of day detection for dynamic lighting
const hour = new Date().getHours();
const isDaytime = hour >= 6 && hour < 18;
const timeOfDay = isDaytime ? 'day' : 'night';
---

<section class="pt-8 pb-12 relative overflow-hidden ghibli-hero ghibli-watercolor ghibli-dust-animated">
  <!-- Enhanced dynamic sky background based on time of day -->
  <div class={`absolute inset-0 ${isDaytime ? 
    'bg-gradient-to-br from-ghibli-spring-100 via-ghibli-earth-50 to-white' : 
    'bg-gradient-to-br from-ghibli-earth-900/90 via-ghibli-earth-800/80 to-ghibli-earth-900/90'
  } opacity-90 transition-colors duration-1000`}></div>
  
  <!-- Enhanced cloud layers -->
  <div class="ghibli-clouds absolute inset-0 opacity-40"></div>
  
  <!-- Dynamic lighting effects -->
  <div class={`absolute inset-0 ghibli-glow ${isDaytime ? 'opacity-40' : 'opacity-20'}`}></div>
  
  <!-- Landscape silhouettes -->
  <div class="ghibli-landscape absolute inset-0 opacity-70"></div>
  
  <!-- Fireflies effect (visible at night) -->
  {!isDaytime && (
    <div class="ghibli-fireflies absolute inset-0"></div>
  )}
  
  <!-- Hand-drawn decorative elements -->
  <div class="absolute top-10 left-1/4 w-40 h-16 bg-white/80 rounded-full blur-md ghibli-wind"></div>
  <div class="absolute top-16 left-1/4 translate-x-10 w-32 h-12 bg-white/70 rounded-full blur-md ghibli-wind" style="animation-delay: -2s"></div>
  <div class="absolute top-8 right-1/4 w-48 h-14 bg-white/80 rounded-full blur-md ghibli-wind" style="animation-delay: -4s"></div>
  <div class="absolute top-20 right-1/4 -translate-x-16 w-24 h-10 bg-white/70 rounded-full blur-md ghibli-wind" style="animation-delay: -1s"></div>
  
  <!-- Enhanced floating elements with parallax effect -->
  <div class="absolute top-1/4 left-10 w-8 h-8 bg-ghibli-warm-300/50 rounded-full animate-pulse" style="animation-duration: 3s; transform: translateZ(-10px);"></div>
  <div class="absolute bottom-1/3 right-20 w-6 h-6 bg-ghibli-spring-400/50 rounded-full animate-pulse" style="animation-duration: 5s; animation-delay: -2s; transform: translateZ(-5px);"></div>
  <div class="absolute top-1/3 right-1/4 w-4 h-4 bg-ghibli-forest-300/50 rounded-full animate-pulse" style="animation-duration: 4s; animation-delay: -1s; transform: translateZ(-15px);"></div>
  <div class="absolute bottom-1/4 left-1/3 w-5 h-5 bg-ghibli-warm-200/50 rounded-full animate-pulse" style="animation-duration: 6s; animation-delay: -3s; transform: translateZ(-8px);"></div>
  
  <!-- Nature elements with hover animations -->
  <div class="absolute top-20 left-1/4 transform rotate-12 transition-transform hover:rotate-45 duration-1000">
    <svg width="32" height="32" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="text-ghibli-spring-500 opacity-60">
      <path d="M12 2L14.5 9.5H21L15.5 14L17.5 21L12 17L6.5 21L8.5 14L3 9.5H9.5L12 2Z" fill="currentColor"/>
    </svg>
  </div>
  <div class="absolute bottom-16 right-1/4 transform -rotate-12 transition-transform hover:-rotate-45 duration-1000">
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="text-ghibli-warm-500 opacity-60">
      <path d="M12 2L14.5 9.5H21L15.5 14L17.5 21L12 17L6.5 21L8.5 14L3 9.5H9.5L12 2Z" fill="currentColor"/>
    </svg>
  </div>
  
  <!-- Animated tree silhouettes -->
  <div class="absolute bottom-0 left-10 w-32 h-48 opacity-20 ghibli-wind">
    <svg viewBox="0 0 100 160" fill="none" xmlns="http://www.w3.org/2000/svg" class={`text-${isDaytime ? 'ghibli-forest-800' : 'ghibli-forest-900'} w-full h-full`}>
      <path d="M50 0C45 20 40 40 45 60C50 80 40 100 50 120C60 140 40 160 50 160H60C70 160 50 140 60 120C70 100 60 80 65 60C70 40 65 20 60 0H50Z" fill="currentColor"/>
    </svg>
  </div>
  <div class="absolute bottom-0 right-20 w-24 h-36 opacity-20 ghibli-wind" style="animation-delay: -3s">
    <svg viewBox="0 0 100 160" fill="none" xmlns="http://www.w3.org/2000/svg" class={`text-${isDaytime ? 'ghibli-forest-800' : 'ghibli-forest-900'} w-full h-full`}>
      <path d="M50 0C45 20 40 40 45 60C50 80 40 100 50 120C60 140 40 160 50 160H60C70 160 50 140 60 120C70 100 60 80 65 60C70 40 65 20 60 0H50Z" fill="currentColor"/>
    </svg>
  </div>
  
  <div class={`container mx-auto px-4 ${containerWidth} relative z-10`}>
    <!-- Enhanced grid layout to accommodate 6 articles -->
    <div class="grid grid-cols-12 grid-rows-6 gap-5 h-[750px]">
      <!-- Featured article with enhanced visual effects -->
      {articles.length > 0 && (
        <div class="col-span-8 row-span-4 group relative">
          <!-- Glow effect border -->
          <div class="absolute -inset-1 bg-gradient-to-r from-ghibli-forest-600 via-ghibli-warm-600 to-ghibli-spring-600 rounded-3xl blur opacity-30 group-hover:opacity-50 transition duration-500"></div>
          
          <a href={`/article/${articles[0].slug}`} class="block h-full relative overflow-hidden rounded-3xl transform hover:-translate-y-1 transition-all duration-700 shadow-ghibli hover:shadow-ghibli-hover">
            <div class="absolute inset-0">
              <!-- Animated image scale on hover -->
              <img 
                src={articles[0].featured_image} 
                alt={articles[0].title} 
                class="w-full h-full object-cover transform group-hover:scale-110 transition-transform duration-1000"
              />
              
              <!-- Enhanced gradient overlay with animation -->
              <div class="absolute inset-0 bg-gradient-to-t from-ghibli-earth-900/90 via-ghibli-earth-900/60 to-transparent opacity-90 group-hover:opacity-80 transition-opacity duration-700"></div>
              
              <!-- Animated dust particles effect -->
              <div class="absolute inset-0 ghibli-dust-animated opacity-30"></div>
            </div>
            
            <div class="absolute bottom-0 left-0 p-8 w-full transform group-hover:translate-y-[-10px] transition-transform duration-700">
              <!-- Enhanced badge with animation -->
              <span class={`inline-flex items-center px-4 py-1.5 bg-gradient-to-r ${categoryColors[0]} text-white text-sm font-medium rounded-full shadow-lg transform group-hover:scale-110 transition-all duration-500`}>
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1.5" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
                </svg>
                Featured
              </span>
              
              <!-- Enhanced title animation -->
              <h2 class="text-2xl sm:text-3xl md:text-4xl font-ghibliHeading font-bold mt-4 text-white drop-shadow-lg transform group-hover:translate-x-2 transition-transform duration-700">
                {articles[0].title}
              </h2>
              
              <!-- Description with animated reveal -->
              <p class="mt-3 text-ghibli-earth-100 text-lg max-w-3xl drop-shadow-lg hidden md:block opacity-80 group-hover:opacity-100 transform group-hover:translate-y-[-5px] transition-all duration-700">
                {articles[0].description.substring(0, 120)}...
              </p>
              
              <!-- Enhanced author section -->
              <div class="flex items-center mt-5">
                <img src="https://randomuser.me/api/portraits/women/1.jpg" alt="Author" class="w-10 h-10 rounded-full border-2 border-ghibli-spring-300 transform group-hover:scale-110 transition-transform duration-500" />
                <div class="ml-3">
                  <span class="block text-white font-medium text-sm drop-shadow-md">{isDaytime ? 'Sarah Johnson' : 'Night Owl Sarah'}</span>
                  <span class="block text-ghibli-earth-200 text-xs drop-shadow-md">{formatDate(articles[0].published_at)}</span>
                </div>
                
                <!-- Animated read more button -->
                <span class="ml-auto text-white group-hover:translate-x-2 transition-transform duration-500 inline-flex items-center font-medium bg-gradient-to-r from-ghibli-warm-600 to-ghibli-sunset-600 px-4 py-1.5 rounded-full shadow-md">
                  Read More
                  <svg class="w-4 h-4 ml-2 transform group-hover:translate-x-1 transition-transform duration-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                  </svg>
                </span>
              </div>
            </div>
          </a>
        </div>
      )}
      
      <!-- Second article with enhanced effects -->
      {articles.length > 1 && (
        <div class="col-span-4 row-span-2 group relative">
          <div class="absolute -inset-1 bg-gradient-to-r from-ghibli-spring-500 via-ghibli-spring-600 to-ghibli-forest-500 rounded-2xl blur opacity-30 group-hover:opacity-50 transition duration-500"></div>
          
          <a href={`/article/${articles[1].slug}`} class="block h-full relative overflow-hidden rounded-2xl transform hover:-translate-y-1 transition-all duration-700 shadow-ghibli hover:shadow-ghibli-hover">
            <div class="absolute inset-0">
              <img 
                src={articles[1].featured_image} 
                alt={articles[1].title} 
                class="w-full h-full object-cover transform group-hover:scale-110 transition-transform duration-1000"
              />
              <!-- Enhanced gradient with animation -->
              <div class="absolute inset-0 bg-gradient-to-t from-ghibli-earth-900/80 via-ghibli-earth-800/50 to-transparent opacity-90 group-hover:opacity-80 transition-opacity duration-700"></div>
              
              <!-- Light particles effect -->
              <div class="absolute inset-0 opacity-0 group-hover:opacity-30 transition-opacity duration-1000 ghibli-fireflies"></div>
            </div>
            
            <div class="absolute bottom-0 left-0 p-5 w-full transform group-hover:translate-y-[-5px] transition-transform duration-700">
              <span class={`inline-flex items-center px-3 py-1 bg-gradient-to-r ${categoryColors[1]} text-white text-xs font-medium rounded-full shadow-md transform group-hover:scale-110 transition-all duration-500`}>
                Review
              </span>
              <h3 class="text-lg font-ghibliHeading font-bold mt-2 text-white drop-shadow-lg line-clamp-2 group-hover:text-ghibli-spring-100 transition-colors duration-500">
                {articles[1].title}
              </h3>
              <div class="flex items-center justify-between mt-3">
                <span class="text-ghibli-earth-100 text-xs drop-shadow-md">{formatDate(articles[1].published_at)}</span>
                <!-- Animated arrow icon -->
                <svg class="w-4 h-4 text-white transform translate-x-0 group-hover:translate-x-1 opacity-0 group-hover:opacity-100 transition-all duration-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                </svg>
              </div>
            </div>
          </a>
        </div>
      )}
      
      <!-- Third article with enhanced effects -->
      {articles.length > 2 && (
        <div class="col-span-4 row-span-2 group relative">
          <div class="absolute -inset-1 bg-gradient-to-r from-ghibli-warm-500 via-ghibli-sunset-500 to-ghibli-warm-600 rounded-2xl blur opacity-30 group-hover:opacity-50 transition duration-500"></div>
          
          <a href={`/article/${articles[2].slug}`} class="block h-full relative overflow-hidden rounded-2xl transform hover:-translate-y-1 transition-all duration-700 shadow-ghibli hover:shadow-ghibli-hover">
            <div class="absolute inset-0">
              <img 
                src={articles[2].featured_image} 
                alt={articles[2].title} 
                class="w-full h-full object-cover transform group-hover:scale-110 transition-transform duration-1000"
              />
              <!-- Enhanced gradient with animation -->
              <div class="absolute inset-0 bg-gradient-to-t from-ghibli-earth-900/80 via-ghibli-earth-800/50 to-transparent opacity-90 group-hover:opacity-80 transition-opacity duration-700"></div>
              
              <!-- Light particles effect -->
              <div class="absolute inset-0 opacity-0 group-hover:opacity-30 transition-opacity duration-1000 ghibli-fireflies"></div>
            </div>
            
            <div class="absolute bottom-0 left-0 p-5 w-full transform group-hover:translate-y-[-5px] transition-transform duration-700">
              <span class={`inline-flex items-center px-3 py-1 bg-gradient-to-r ${categoryColors[2]} text-white text-xs font-medium rounded-full shadow-md transform group-hover:scale-110 transition-all duration-500`}>
                Guide
              </span>
              <h3 class="text-lg font-ghibliHeading font-bold mt-2 text-white drop-shadow-lg line-clamp-2 group-hover:text-ghibli-spring-100 transition-colors duration-500">
                {articles[2].title}
              </h3>
              <div class="flex items-center justify-between mt-3">
                <span class="text-ghibli-earth-100 text-xs drop-shadow-md">{formatDate(articles[2].published_at)}</span>
                <!-- Animated arrow icon -->
                <svg class="w-4 h-4 text-white transform translate-x-0 group-hover:translate-x-1 opacity-0 group-hover:opacity-100 transition-all duration-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                </svg>
              </div>
            </div>
          </a>
        </div>
      )}
      
      <!-- Fourth article with enhanced watercolor effect -->
      {articles.length > 3 && (
        <div class="col-span-4 row-span-2 group relative">
          <div class="absolute -inset-1 bg-gradient-to-r from-ghibli-forest-500 via-ghibli-forest-600 to-ghibli-spring-500 rounded-2xl blur opacity-30 group-hover:opacity-50 transition duration-500"></div>
          
          <a href={`/article/${articles[3].slug}`} class="block h-full relative overflow-hidden rounded-2xl bg-white transform hover:-translate-y-1 transition-all duration-700 shadow-ghibli hover:shadow-ghibli-hover">
            <div class="flex h-full">
              <div class="w-1/3 h-full relative overflow-hidden">
                <img 
                  src={articles[3].featured_image} 
                  alt={articles[3].title} 
                  class="w-full h-full object-cover transform group-hover:scale-110 transition-transform duration-1000"
                />
                <div class="absolute inset-0 bg-gradient-to-r from-transparent to-black/20 group-hover:opacity-70 transition-opacity duration-700"></div>
              </div>
              
              <!-- Enhanced background with watercolor effect -->
              <div class="w-2/3 p-4 flex flex-col justify-center bg-gradient-to-br from-white to-ghibli-earth-50 relative ghibli-watercolor">
                <span class={`inline-flex items-center px-2 py-1 bg-gradient-to-r ${categoryColors[3]} text-white text-xs font-medium rounded-full shadow-sm mb-2 transform group-hover:scale-110 transition-all duration-500`}>
                  Review
                </span>
                <h3 class="text-sm font-ghibliHeading font-bold text-ghibli-forest-800 group-hover:text-ghibli-warm-600 transition-colors duration-500 line-clamp-2">
                  {articles[3].title}
                </h3>
                <span class="mt-2 text-xs text-ghibli-earth-600">{formatDate(articles[3].published_at)}</span>
                
                <!-- Animated read indicator -->
                <div class="absolute bottom-2 right-2 w-5 h-5 rounded-full bg-gradient-to-r from-ghibli-spring-500 to-ghibli-forest-500 opacity-0 group-hover:opacity-100 transition-opacity duration-700 flex items-center justify-center">
                  <svg class="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                  </svg>
                </div>
              </div>
            </div>
          </a>
        </div>
      )}
      
      <!-- Fifth article with enhanced hand-drawn effect -->
      {articles.length > 4 && (
        <div class="col-span-4 row-span-2 group relative">
          <div class="absolute -inset-1 bg-gradient-to-r from-ghibli-earth-600 via-ghibli-earth-700 to-ghibli-warm-600 rounded-2xl blur opacity-30 group-hover:opacity-50 transition duration-500"></div>
          
          <a href={`/article/${articles[4].slug}`} class="block h-full relative overflow-hidden rounded-2xl bg-white transform hover:-translate-y-1 transition-all duration-700 shadow-ghibli hover:shadow-ghibli-hover">
            <div class="flex h-full">
              <div class="w-1/3 h-full relative overflow-hidden">
                <img 
                  src={articles[4].featured_image} 
                  alt={articles[4].title} 
                  class="w-full h-full object-cover transform group-hover:scale-110 transition-transform duration-1000"
                />
                <div class="absolute inset-0 bg-gradient-to-r from-transparent to-black/20 group-hover:opacity-70 transition-opacity duration-700"></div>
              </div>
              
              <!-- Enhanced background with hand-drawn border effect -->
              <div class="w-2/3 p-4 flex flex-col justify-center bg-gradient-to-br from-white to-ghibli-earth-50 relative ghibli-border border-ghibli-earth-200">
                <span class={`inline-flex items-center px-2 py-1 bg-gradient-to-r ${categoryColors[4]} text-white text-xs font-medium rounded-full shadow-sm mb-2 transform group-hover:scale-110 transition-all duration-500`}>
                  Guide
                </span>
                <h3 class="text-sm font-ghibliHeading font-bold text-ghibli-forest-800 group-hover:text-ghibli-warm-600 transition-colors duration-500 line-clamp-2">
                  {articles[4].title}
                </h3>
                <span class="mt-2 text-xs text-ghibli-earth-600">{formatDate(articles[4].published_at)}</span>
                
                <!-- Animated read indicator -->
                <div class="absolute bottom-2 right-2 w-5 h-5 rounded-full bg-gradient-to-r from-ghibli-earth-500 to-ghibli-warm-500 opacity-0 group-hover:opacity-100 transition-opacity duration-700 flex items-center justify-center">
                  <svg class="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                  </svg>
                </div>
              </div>
            </div>
          </a>
        </div>
      )}
      
      <!-- NEW: Sixth article with special spotlight effect -->
      {articles.length > 5 && (
        <div class="col-span-8 row-span-2 group relative">
          <div class="absolute -inset-1 bg-gradient-to-r from-ghibli-warm-500 via-ghibli-forest-500 to-ghibli-spring-600 rounded-2xl blur opacity-30 group-hover:opacity-50 transition duration-500"></div>
          
          <a href={`/article/${articles[5].slug}`} class="block h-full relative overflow-hidden rounded-2xl transform hover:-translate-y-1 transition-all duration-700 shadow-ghibli hover:shadow-ghibli-hover">
            <div class="flex h-full">
              <div class="w-1/3 h-full relative overflow-hidden">
                <img 
                  src={articles[5].featured_image} 
                  alt={articles[5].title} 
                  class="w-full h-full object-cover transform group-hover:scale-110 transition-transform duration-1000"
                />
                <div class="absolute inset-0 bg-gradient-to-r from-transparent to-black/20 group-hover:opacity-70 transition-opacity duration-700"></div>
                
                <!-- Spotlight effect -->
                <div class="absolute inset-0 bg-radial-gradient from-white/30 to-transparent opacity-0 group-hover:opacity-100 transition-all duration-700"></div>
              </div>
              
              <div class="w-2/3 p-5 flex flex-col justify-center bg-gradient-to-br from-white to-ghibli-earth-50 relative ghibli-watercolor">
                <div class="flex items-center mb-3">
                  <span class={`inline-flex items-center px-3 py-1 bg-gradient-to-r ${categoryColors[5]} text-white text-xs font-medium rounded-full shadow-md transform group-hover:scale-110 transition-all duration-500`}>
                    Featured Deal
                  </span>
                  <span class="ml-2 text-ghibli-earth-600 text-xs">{formatDate(articles[5].published_at)}</span>
                </div>
                
                <h3 class="text-lg font-ghibliHeading font-bold text-ghibli-forest-800 group-hover:text-ghibli-warm-600 transition-colors duration-500 line-clamp-2 mb-2">
                  {articles[5].title}
                </h3>
                
                <p class="text-sm text-ghibli-earth-700 line-clamp-2 mb-3 opacity-80 group-hover:opacity-100 transition-opacity duration-500">
                  {articles[5].description.substring(0, 120)}...
                </p>
                
                <!-- Special animated button -->
                <div class="flex items-center mt-auto">
                  <span class="inline-flex items-center font-medium text-ghibli-warm-600 group-hover:text-ghibli-warm-700 transition-colors duration-500">
                    Read More
                    <svg class="w-4 h-4 ml-1 transform translate-x-0 group-hover:translate-x-1 transition-transform duration-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                    </svg>
                  </span>
                </div>
              </div>
            </div>
          </a>
        </div>
      )}
    </div>
  </div>
</section>

<style>
  .ghibli-dust {
    background-image: radial-gradient(circle, rgba(255,255,255,0.4) 1px, transparent 1px);
    background-size: 20px 20px;
  }
  
  .shadow-ghibli {
    box-shadow: 0 10px 15px -3px rgba(134, 115, 87, 0.1), 0 4px 6px -2px rgba(134, 115, 87, 0.05);
  }
  
  .shadow-ghibli-hover {
    box-shadow: 0 20px 25px -5px rgba(134, 115, 87, 0.2), 0 10px 10px -5px rgba(134, 115, 87, 0.1);
  }
  
  .ghibli-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 5px;
    background: linear-gradient(to right, 
      #e4a672, #e4a672 10%, 
      #8e9b5e, #8e9b5e 20%, 
      #d8745e, #d8745e 30%,
      #b1b479, #b1b479 40%,
      #867357, #867357 50%,
      #e4a672, #e4a672 60%,
      #8e9b5e, #8e9b5e 70%,
      #d8745e, #d8745e 80%,
      #b1b479, #b1b479 90%,
      #867357, #867357 100%
    );
  }
  
  /* Enhanced parallax scrolling effect */
  @media (prefers-reduced-motion: no-preference) {
    .ghibli-hero {
      perspective: 1000px;
    }
    
    .ghibli-hero > * {
      transform-style: preserve-3d;
    }
    
    @keyframes floatingElement {
      0%, 100% { transform: translateY(0) translateZ(0); }
      50% { transform: translateY(-5px) translateZ(10px); }
    }
    
    .ghibli-hero .ghibli-floating {
      animation: floatingElement 6s ease-in-out infinite;
    }
  }
  
  /* Day/night transition effect */
  .ghibli-hero.day-transition {
    transition: background-color 3s ease;
  }
  
  /* Radial gradient for spotlight effect */
  .bg-radial-gradient {
    background: radial-gradient(circle at center, var(--tw-gradient-from), var(--tw-gradient-to));
  }
</style>

<script>
  // Add interactive animations based on scroll position
  document.addEventListener('DOMContentLoaded', () => {
    const hero = document.querySelector('.ghibli-hero');
    
    if (hero) {
      window.addEventListener('scroll', () => {
        const scrollY = window.scrollY;
        const opacity = Math.max(0, 1 - scrollY / 500);
        
        // Apply parallax effect to background elements
        const clouds = hero.querySelector('.ghibli-clouds') as HTMLElement;
        if (clouds) {
          clouds.style.transform = `translateY(${scrollY * 0.05}px)`;
        }
        
        // Fade out elements on scroll
        (hero as HTMLElement).style.opacity = opacity.toString();
      });
      
      // Add hover effects to articles
      const articles = hero.querySelectorAll('a');
      articles.forEach(article => {
        article.addEventListener('mouseenter', () => {
          // Add light glow effect on hover
          article.classList.add('ghibli-glow');
        });
        
        article.addEventListener('mouseleave', () => {
          article.classList.remove('ghibli-glow');
        });
      });
    }
  });
</script>