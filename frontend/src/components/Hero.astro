---
import { siteConfig } from '../config/site';
import { ArticleDetailResp } from '../lib/api';

// Prop for receiving articles from the parent
interface Props {
  articles: ArticleDetailResp[];
}

const { articles } = Astro.props;

// Ensure we have at least 3 articles for the hero layout
const ensuredArticles = articles.length >= 3 ? articles.slice(0, 3) : [
  ...articles,
  ...Array(3 - articles.length).fill(null).map((_, index) => ({
    id: 999 + index,
    slug: `fallback-article-${index}`,
    title: "Discover Amazing Tech Reviews",
    description: "Explore our comprehensive reviews and guides to make informed decisions about the latest technology products.",
    content: "",
    featured_image: `https://images.unsplash.com/photo-${1500000000000 + index}?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80`,
    category: { name: "Technology" },
    publish_date: new Date().toISOString(),
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  }))
].slice(0, 3);

// Helper function to get image URL with fallback
function getImageUrl(article: any, index: number): string {
  if (article?.featured_image) {
    return article.featured_image;
  }
  const fallbackImages = [
    'https://images.unsplash.com/photo-1518709268805-4e9042af2176?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
    'https://images.unsplash.com/photo-1542831371-29b0f74f9713?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
    'https://images.unsplash.com/photo-1593508512255-86ab42a8e620?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80'
  ];
  return fallbackImages[index % fallbackImages.length];
}

// Helper function to format date
function formatDate(dateString: string): string {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
}

// Helper function to get category name with fallback
function getCategoryName(article: any): string {
  return article?.category?.name || "Technology";
}
---

<!-- Enhanced Hero Section with 2025 Ghibli Magic -->
<section class="relative min-h-screen flex items-center justify-center overflow-hidden" style="background: linear-gradient(135deg, rgba(142, 155, 94, 0.9) 0%, rgba(228, 166, 114, 0.8) 30%, rgba(216, 116, 94, 0.9) 70%, rgba(177, 180, 121, 0.95) 100%);">
  <!-- Atmospheric Background Effects -->
  <div class="absolute inset-0 opacity-30">
    <div class="absolute top-0 right-0 w-96 h-96 bg-gradient-radial from-white/25 to-transparent blur-3xl animate-pulse"></div>
    <div class="absolute bottom-0 left-0 w-80 h-80 bg-gradient-radial from-yellow-300/20 to-transparent blur-3xl animate-pulse" style="animation-delay: -2s;"></div>
  </div>
  
  <!-- Main Content -->
  <div class="container mx-auto px-4 py-20 relative z-10">
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8 max-w-7xl mx-auto">
      
      <!-- Main Featured Article -->
      <article class="lg:col-span-2 bg-white/90 backdrop-blur-sm rounded-3xl overflow-hidden shadow-2xl hover:shadow-3xl transition-all duration-500 group" data-ghibli-animate="fadeInUp">
        <a href={`/article/${ensuredArticles[0].slug}`} class="block">
          <div class="relative h-64 lg:h-80 overflow-hidden">
            <img 
              src={getImageUrl(ensuredArticles[0], 0)} 
              alt={ensuredArticles[0].title} 
              class="w-full h-full object-cover transition-transform duration-700 group-hover:scale-110" 
            />
            <div class="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent"></div>
            <div class="absolute top-4 left-4 bg-gradient-to-r from-orange-500 to-red-500 text-white px-3 py-1 rounded-full text-sm font-semibold">
              {getCategoryName(ensuredArticles[0])}
            </div>
          </div>
          
          <div class="p-8">
            <h2 class="text-3xl lg:text-4xl font-bold text-gray-800 mb-4 group-hover:text-orange-700 transition-colors leading-tight">
              {ensuredArticles[0].title.length > 70 ? `${ensuredArticles[0].title.substring(0, 70)}...` : ensuredArticles[0].title}
            </h2>
            <p class="text-gray-600 text-lg mb-6 leading-relaxed">
              {ensuredArticles[0].description && ensuredArticles[0].description.length > 140
                ? `${ensuredArticles[0].description.substring(0, 140)}...`
                : ensuredArticles[0].description}
            </p>
            
            <div class="flex items-center justify-between">
              <div class="flex items-center text-gray-500 text-sm">
                <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd" />
                </svg>
                {formatDate(ensuredArticles[0].publish_date)}
              </div>
              <span class="inline-flex items-center bg-gradient-to-r from-orange-500 to-red-500 text-white font-semibold px-4 py-2 rounded-xl transition-all duration-300 group-hover:shadow-lg group-hover:scale-105">
                Read More
                <svg class="w-4 h-4 ml-2 transition-transform group-hover:translate-x-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3" />
                </svg>
              </span>
            </div>
          </div>
        </a>
      </article>
      
      <!-- Secondary Articles -->
      <div class="space-y-6">
        {ensuredArticles.slice(1, 3).map((article, index) => (
          <article class="bg-white/90 backdrop-blur-sm rounded-2xl overflow-hidden shadow-xl hover:shadow-2xl transition-all duration-500 group" data-ghibli-animate="fadeInUp" style={`animation-delay: ${(index + 1) * 200}ms;`}>
            <a href={`/article/${article.slug}`} class="block">
              <div class="relative h-32 overflow-hidden">
                <img 
                  src={getImageUrl(article, index + 1)} 
                  alt={article.title} 
                  class="w-full h-full object-cover transition-transform duration-700 group-hover:scale-110" 
                />
                <div class="absolute inset-0 bg-gradient-to-t from-black/50 via-transparent to-transparent"></div>
                <div class="absolute top-2 left-2 bg-gradient-to-r from-blue-500 to-purple-500 text-white px-2 py-1 rounded-full text-xs font-semibold">
                  {getCategoryName(article)}
                </div>
              </div>
              
              <div class="p-4">
                <h3 class="text-lg font-bold text-gray-800 mb-2 group-hover:text-orange-700 transition-colors leading-tight">
                  {article.title.length > 50 ? `${article.title.substring(0, 50)}...` : article.title}
                </h3>
                <p class="text-gray-600 text-sm mb-3 leading-relaxed">
                  {article.description && article.description.length > 80
                    ? `${article.description.substring(0, 80)}...`
                    : article.description}
                </p>
                
                <div class="flex items-center justify-between">
                  <div class="text-gray-500 text-xs">
                    {formatDate(article.publish_date)}
                  </div>
                  <span class="text-orange-600 font-semibold text-sm group-hover:text-orange-700 transition-colors">
                    Read More →
                  </span>
                </div>
              </div>
            </a>
          </article>
        ))}
      </div>
    </div>
  </div>
</section>

<style>
  /* Essential animations */
  @keyframes fadeInUp {
    from { opacity: 0; transform: translateY(30px); }
    to { opacity: 1; transform: translateY(0); }
  }
  
  [data-ghibli-animate="fadeInUp"] {
    animation: fadeInUp 0.8s ease-out forwards;
  }
  
  .bg-gradient-radial {
    background: radial-gradient(circle, var(--tw-gradient-stops));
  }
  
  @media (prefers-reduced-motion: reduce) {
    * { animation-duration: 0.01ms !important; }
  }
</style>
