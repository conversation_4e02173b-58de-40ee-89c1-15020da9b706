---
import { siteConfig } from '../config/site';
import { ArticleDetailResp } from '../lib/api';
const { containerWidth } = siteConfig;

// Prop for receiving articles from the parent
interface Props {
  articles: ArticleDetailResp[];
}

const { articles } = Astro.props;

// Define a type that includes both API articles and our fallback articles
type ArticleType = ArticleDetailResp | {
  id: number;
  slug: string;
  title: string;
  description: string;
  content: string;
  featured_image: string;
  category?: { name: string };
  publish_date: string;
  created_at: string;
  updated_at: string;
};

// Time-based dynamic styling
const hour = new Date().getHours();
const isDaytime = hour >= 6 && hour < 18;
const isGoldenHour = (hour >= 6 && hour <= 8) || (hour >= 17 && hour <= 19);
const isNight = hour >= 20 || hour <= 5;

// Dynamic color schemes based on time
const timeBasedColors = {
  day: {
    primary: 'rgba(240, 244, 255, 0.95)',
    secondary: 'rgba(232, 241, 255, 0.9)',
    accent: 'rgba(142, 155, 94, 0.8)',
  },
  golden: {
    primary: 'rgba(255, 248, 220, 0.95)',
    secondary: 'rgba(255, 239, 195, 0.9)',
    accent: 'rgba(228, 166, 114, 0.8)',
  },
  night: {
    primary: 'rgba(30, 41, 59, 0.95)',
    secondary: 'rgba(51, 65, 85, 0.9)',
    accent: 'rgba(134, 115, 87, 0.8)',
  }
};

const currentColors = isNight ? timeBasedColors.night :
                     isGoldenHour ? timeBasedColors.golden :
                     timeBasedColors.day;

// Ensure we have articles to display
const ensuredArticles: ArticleType[] = articles && articles.length >= 6 ? articles : [
  {
    id: 1,
    slug: 'default-article-1',
    title: 'Experience the future of technology with our comprehensive guides',
    description: 'Discover the latest innovations and expert insights that will transform your digital experience.',
    content: '',
    featured_image: 'https://images.unsplash.com/photo-1531297484001-80022131f5a1?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80',
    category: { name: 'Technology' },
    publish_date: new Date().toISOString(),
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  },
  {
    id: 2,
    slug: 'default-article-2',
    title: 'The ultimate guide to choosing the perfect headphones',
    description: 'Find the perfect balance of sound quality, comfort, and price with our expert recommendations.',
    content: '',
    featured_image: 'https://images.unsplash.com/photo-1505740420928-5e560c06d30e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80',
    category: { name: 'Audio' },
    publish_date: new Date().toISOString(),
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  },
  {
    id: 3,
    slug: 'default-article-3',
    title: 'Smart home devices that actually improve your daily life',
    description: 'Cut through the hype and discover which smart home devices are truly worth investing in.',
    content: '',
    featured_image: 'https://images.unsplash.com/photo-1558002038-2f2768376a25?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80',
    category: { name: 'Smart Home' },
    publish_date: new Date().toISOString(),
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  },
  {
    id: 4,
    slug: 'default-article-4',
    title: 'The best photography gear for beginners and enthusiasts',
    description: 'Start your photography journey right with our hand-picked selection of cameras and accessories.',
    content: '',
    featured_image: 'https://images.unsplash.com/photo-1516035069371-29a1b244cc32?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80',
    category: { name: 'Photography' },
    publish_date: new Date().toISOString(),
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  },
  {
    id: 5,
    slug: 'default-article-5',
    title: 'Essential gaming accessories to enhance your experience',
    description: 'Level up your gaming setup with these must-have peripherals and accessories.',
    content: '',
    featured_image: 'https://images.unsplash.com/photo-1593305841991-05c297ba4575?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80',
    category: { name: 'Gaming' },
    publish_date: new Date().toISOString(),
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  },
  {
    id: 6,
    slug: 'default-article-6',
    title: 'The best laptops for every type of user in 2023',
    description: 'From casual browsing to intensive gaming, find the perfect laptop for your specific needs.',
    content: '',
    featured_image: 'https://images.unsplash.com/photo-1496181133206-80ce9b88a853?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80',
    category: { name: 'Computers' },
    publish_date: new Date().toISOString(),
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  }
];

// Helper function for formatting dates
function formatDate(dateString: string): string {
  try {
    const options: Intl.DateTimeFormatOptions = { year: 'numeric', month: 'short', day: 'numeric' };
    return new Date(dateString).toLocaleDateString('en-US', options);
  } catch (e) {
    return 'Recent';
  }
}

// Time of day detection for dynamic styling (using existing hour variable)

// Helper function to get image URL with fallback
function getImageUrl(article: ArticleType, index: number): string {
  return article.featured_image || 
         (article as ArticleDetailResp).cover_image || 
         `https://source.unsplash.com/random/800x600?tech,${index}`;
}

// Helper function to get category name with fallback
function getCategoryName(article: ArticleType): string {
  return article.category?.name || "Uncategorized";
}
---

<!-- Ultra-Enhanced Hero Canvas with 2025 Ghibli Magic -->
<div class="hero-canvas-enhanced ghibli-dynamic-bg ghibli-particles-enhanced ghibli-mouse-light ghibli-aurora ghibli-watercolor ghibli-wind ghibli-fireflies ghibli-clouds ghibli-crystal-atmosphere" data-time-of-day={isDaytime ? 'day' : isGoldenHour ? 'golden' : 'night'}>
  <!-- Multi-layered Parallax Background with Advanced Effects -->
  <div class="ghibli-parallax-container ghibli-depth-blur">
    <!-- Far Background Layer -->
    <div class="ghibli-parallax-layer ghibli-parallax-back">
      <div class="absolute inset-0 opacity-30">
        <svg class="w-full h-full" viewBox="0 0 1000 600" preserveAspectRatio="xMidYMid slice">
          <defs>
            <linearGradient id="skyGradient" x1="0%" y1="0%" x2="0%" y2="100%">
              <stop offset="0%" style={`stop-color:${currentColors.primary};stop-opacity:1`} />
              <stop offset="100%" style={`stop-color:${currentColors.secondary};stop-opacity:1`} />
            </linearGradient>
          </defs>
          <rect width="1000" height="600" fill="url(#skyGradient)" />
          <!-- Floating clouds -->
          <g class="ghibli-float-physics">
            <ellipse cx="200" cy="150" rx="80" ry="30" fill="rgba(255,255,255,0.4)" />
            <ellipse cx="180" cy="140" rx="60" ry="25" fill="rgba(255,255,255,0.3)" />
          </g>
          <g class="ghibli-float-physics" style="animation-delay: -3s;">
            <ellipse cx="600" cy="100" rx="100" ry="40" fill="rgba(255,255,255,0.3)" />
            <ellipse cx="580" cy="90" rx="70" ry="30" fill="rgba(255,255,255,0.2)" />
          </g>
          <g class="ghibli-float-physics" style="animation-delay: -6s;">
            <ellipse cx="800" cy="180" rx="90" ry="35" fill="rgba(255,255,255,0.35)" />
          </g>
        </svg>
      </div>
    </div>

    <!-- Mid Background Layer -->
    <div class="ghibli-parallax-layer ghibli-parallax-mid">
      <div class="absolute inset-0 opacity-20">
        <!-- Distant mountains -->
        <svg class="w-full h-full" viewBox="0 0 1000 600" preserveAspectRatio="xMidYMid slice">
          <path d="M0,400 Q200,350 400,380 T800,360 L1000,370 L1000,600 L0,600 Z" fill={currentColors.accent} opacity="0.3"/>
          <path d="M0,450 Q300,400 600,430 T1000,420 L1000,600 L0,600 Z" fill={currentColors.accent} opacity="0.2"/>
        </svg>
      </div>
    </div>

    <!-- Foreground Content Layer -->
    <div class="ghibli-parallax-layer ghibli-parallax-front relative z-10">
      <div class="container mx-auto px-4 h-full flex items-center">
        <div class="ghibli-scene-enhanced">
          <!-- Ultra-Enhanced Main Card with 2025 Ghibli Magic -->
          <article class="ghibli-main-card-enhanced ghibli-glow-enhanced ghibli-depth-blur ghibli-shimmer ghibli-float-physics group relative" data-depth="0.1" data-ghibli-animate="glowIn" data-ghibli-interactive>
            <a href={`/article/${ensuredArticles[0].slug}`} class="block absolute inset-0 z-30">
              <span class="sr-only">Read article: {ensuredArticles[0].title}</span>
            </a>

            <!-- Advanced dynamic overlay with magical effects -->
            <div class="absolute inset-0 bg-gradient-to-br from-transparent via-transparent to-black/10 opacity-0 group-hover:opacity-100 transition-all duration-500 z-20 ghibli-rainbow-shimmer"></div>

            <!-- Magical border effect -->
            <div class="absolute inset-0 rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-500 z-10" style="background: linear-gradient(45deg, rgba(142, 155, 94, 0.3), rgba(228, 166, 114, 0.3), rgba(216, 116, 94, 0.3), rgba(177, 180, 121, 0.3)); background-size: 400% 400%; animation: gradientShift 3s ease infinite;"></div>

            <div class="ghibli-img-container-enhanced ghibli-crystal-atmosphere">
              <img src={getImageUrl(ensuredArticles[0], 0)} alt={ensuredArticles[0].title} class="ghibli-img-enhanced group-hover:scale-110 transition-transform duration-700" />
              <div class="ghibli-img-overlay-enhanced ghibli-aurora"></div>
              <div class="ghibli-category-enhanced ghibli-sparkle" style="font-family: var(--font-ghibli-primary);">{getCategoryName(ensuredArticles[0])}</div>

              <!-- Enhanced floating particles and magical effects -->
              <div class="absolute inset-0 ghibli-particles-enhanced opacity-30"></div>
              <div class="absolute inset-0 ghibli-fireflies opacity-20"></div>
              <div class="absolute top-4 right-4 text-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-500 ghibli-float-physics">✨</div>
            </div>

            <div class="ghibli-content-enhanced ghibli-glow-enhanced">
              <h2 class="ghibli-title-enhanced ghibli-text-glow" style="font-family: var(--font-ghibli-heading);">
                <span class="hover:underline group-hover:text-ghibli-warm-700 transition-all duration-300 ghibli-sparkle">
                  {ensuredArticles[0].title.length > 70 ? `${ensuredArticles[0].title.substring(0, 70)}...` : ensuredArticles[0].title}
                </span>
              </h2>
              <p class="ghibli-description-enhanced mb-6 ghibli-shimmer" style="font-family: var(--font-ghibli-body);">
                {ensuredArticles[0].description && ensuredArticles[0].description.length > 140
                  ? `${ensuredArticles[0].description.substring(0, 140)}...`
                  : ensuredArticles[0].description}
              </p>
              <div class="flex items-center justify-between mt-6">
                <div class="ghibli-date-enhanced ghibli-glow-enhanced" style="font-family: var(--font-ghibli-accent);">
                  <svg class="w-4 h-4 mr-2 opacity-60 ghibli-float-physics" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd" />
                  </svg>
                  {formatDate(ensuredArticles[0].publish_date)}
                </div>
                <span class="ghibli-button-enhanced group-hover:shadow-xl transition-all duration-300 transform group-hover:translate-x-2 group-hover:-translate-y-1 ghibli-shimmer ghibli-sparkle" style="font-family: var(--font-ghibli-primary);" data-ghibli-interactive>
                  <span>Read More ✨</span>
                  <svg class="w-4 h-4 ml-2 transform group-hover:translate-x-1 transition-transform ghibli-float-physics" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3" />
                  </svg>
                </span>
              </div>
            </div>
          </article>
      
          <!-- Enhanced Secondary Card 1 -->
          <article class="ghibli-secondary-card-enhanced-1 ghibli-glow-enhanced group relative overflow-hidden" data-depth="0.2">
            <a href={`/article/${ensuredArticles[1].slug}`} class="block absolute inset-0 z-30">
              <span class="sr-only">Read article: {ensuredArticles[1].title}</span>
            </a>

            <!-- Dynamic gradient overlay -->
            <div class="absolute inset-0 bg-gradient-to-br from-ghibli-forest-600/5 via-transparent to-ghibli-forest-700/10 opacity-0 group-hover:opacity-100 transition-all duration-500 z-10"></div>

            <!-- Floating decorative elements -->
            <div class="absolute top-4 right-4 w-6 h-6 opacity-20 group-hover:opacity-40 transition-opacity duration-300">
              <svg viewBox="0 0 24 24" fill="currentColor" class="text-ghibli-spring-400 ghibli-float-physics">
                <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
              </svg>
            </div>

            <div class="ghibli-img-container-enhanced">
              <img src={getImageUrl(ensuredArticles[1], 1)} alt={ensuredArticles[1].title} class="ghibli-img-enhanced" />
              <div class="ghibli-img-overlay-enhanced"></div>
              <div class="ghibli-category-enhanced secondary">{getCategoryName(ensuredArticles[1])}</div>
            </div>

            <div class="ghibli-content-enhanced secondary">
              <h3 class="ghibli-title-secondary">
                <span class="group-hover:text-ghibli-warm-700 transition-all duration-300">
                  {ensuredArticles[1].title.length > 55 ? `${ensuredArticles[1].title.substring(0, 55)}...` : ensuredArticles[1].title}
                </span>
              </h3>
              <p class="ghibli-description-secondary">
                {ensuredArticles[1].description && ensuredArticles[1].description.length > 80
                  ? `${ensuredArticles[1].description.substring(0, 80)}...`
                  : ensuredArticles[1].description}
              </p>

              <!-- Enhanced action button -->
              <div class="absolute bottom-4 right-4 w-10 h-10 rounded-full bg-white/0 group-hover:bg-white/95 flex items-center justify-center transform translate-y-6 opacity-0 group-hover:translate-y-0 group-hover:opacity-100 transition-all duration-400 shadow-lg group-hover:shadow-xl">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-ghibli-warm-600 transform group-hover:scale-110 transition-transform" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3" />
                </svg>
              </div>
            </div>
          </article>
      
          <!-- Enhanced Secondary Card 2 -->
          <article class="ghibli-secondary-card-enhanced-2 ghibli-glow-enhanced group relative overflow-hidden" data-depth="0.15">
            <a href={`/article/${ensuredArticles[2].slug}`} class="block absolute inset-0 z-30">
              <span class="sr-only">Read article: {ensuredArticles[2].title}</span>
            </a>

            <!-- Dynamic warm gradient overlay -->
            <div class="absolute inset-0 bg-gradient-to-br from-ghibli-warm-500/8 via-transparent to-ghibli-warm-600/12 opacity-0 group-hover:opacity-100 transition-all duration-500 z-10"></div>

            <!-- Floating leaf decoration -->
            <div class="absolute top-3 left-4 w-5 h-5 opacity-25 group-hover:opacity-50 transition-opacity duration-300">
              <svg viewBox="0 0 24 24" fill="currentColor" class="text-ghibli-warm-400 ghibli-float-physics" style="animation-delay: -2s;">
                <path d="M17,8C8,10 5.9,16.17 3.82,21.34L5.71,22L6.66,19.7C7.14,19.87 7.64,20 8,20C19,20 22,3 22,3C21,5 14,5.25 9,6.25C4,7.25 2,11.5 2,13.5C2,15.5 3.75,17.25 3.75,17.25C7,8 17,8 17,8Z"/>
              </svg>
            </div>

            <div class="ghibli-img-container-enhanced">
              <img src={getImageUrl(ensuredArticles[2], 2)} alt={ensuredArticles[2].title} class="ghibli-img-enhanced" />
              <div class="ghibli-img-overlay-enhanced"></div>
              <div class="ghibli-category-enhanced warm">{getCategoryName(ensuredArticles[2])}</div>
            </div>

            <div class="ghibli-content-enhanced secondary">
              <h3 class="ghibli-title-secondary">
                <span class="group-hover:text-ghibli-warm-700 transition-all duration-300">
                  {ensuredArticles[2].title.length > 55 ? `${ensuredArticles[2].title.substring(0, 55)}...` : ensuredArticles[2].title}
                </span>
              </h3>
              <p class="ghibli-description-secondary">
                {ensuredArticles[2].description && ensuredArticles[2].description.length > 80
                  ? `${ensuredArticles[2].description.substring(0, 80)}...`
                  : ensuredArticles[2].description}
              </p>

              <!-- Enhanced action button with warm theme -->
              <div class="absolute bottom-4 right-4 w-10 h-10 rounded-full bg-gradient-to-br from-white/0 to-white/0 group-hover:from-white/95 group-hover:to-white/90 flex items-center justify-center transform translate-y-6 opacity-0 group-hover:translate-y-0 group-hover:opacity-100 transition-all duration-400 shadow-lg group-hover:shadow-xl">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-ghibli-warm-600 transform group-hover:scale-110 transition-transform" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3" />
                </svg>
              </div>
            </div>
          </article>
      
          <!-- Enhanced Horizontal Card -->
          <article class="ghibli-horizontal-card-enhanced ghibli-glow-enhanced group relative" data-depth="0.05">
            <a href={`/article/${ensuredArticles[3].slug}`} class="block absolute inset-0 z-30">
              <span class="sr-only">Read article: {ensuredArticles[3].title}</span>
            </a>

            <!-- Spring-themed gradient overlay -->
            <div class="absolute inset-0 bg-gradient-to-r from-ghibli-spring-500/6 via-transparent to-ghibli-spring-600/10 opacity-0 group-hover:opacity-100 transition-all duration-500 z-10"></div>

            <!-- Animated nature elements -->
            <div class="absolute top-1/2 left-2 transform -translate-y-1/2 opacity-20 group-hover:opacity-40 transition-opacity duration-300">
              <svg viewBox="0 0 24 24" fill="currentColor" class="w-4 h-4 text-ghibli-spring-500 ghibli-float-physics" style="animation-delay: -4s;">
                <path d="M12,2A3,3 0 0,1 15,5V11A3,3 0 0,1 12,14A3,3 0 0,1 9,11V5A3,3 0 0,1 12,2M19,11C19,14.53 16.39,17.44 13,17.93V21H11V17.93C7.61,17.44 5,14.53 5,11H7A5,5 0 0,0 12,16A5,5 0 0,0 17,11H19Z"/>
              </svg>
            </div>

            <div class="ghibli-img-container-horizontal">
              <img src={getImageUrl(ensuredArticles[3], 3)} alt={ensuredArticles[3].title} class="ghibli-img-enhanced" />
              <div class="ghibli-img-overlay-enhanced"></div>
            </div>

            <div class="ghibli-content-horizontal">
              <h3 class="ghibli-title-horizontal">
                <span class="group-hover:text-ghibli-warm-700 transition-all duration-300">
                  {ensuredArticles[3].title.length > 50 ? `${ensuredArticles[3].title.substring(0, 50)}...` : ensuredArticles[3].title}
                </span>
              </h3>
              <div class="ghibli-date-horizontal mt-3">
                <svg class="w-3 h-3 mr-1.5 opacity-60" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd" />
                </svg>
                {formatDate(ensuredArticles[3].publish_date)}
              </div>

              <!-- Enhanced horizontal action button -->
              <div class="absolute bottom-3 right-3 w-9 h-9 rounded-full bg-white/0 group-hover:bg-white/95 flex items-center justify-center transform translate-y-5 opacity-0 group-hover:translate-y-0 group-hover:opacity-100 transition-all duration-400 shadow-md group-hover:shadow-lg">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-ghibli-spring-600 transform group-hover:scale-110 transition-transform" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3" />
                </svg>
              </div>
            </div>
          </article>
      
          <!-- Enhanced Mini Cards -->
          <div class="ghibli-mini-cards-enhanced">
            <article class="ghibli-mini-card-enhanced-1 ghibli-glow-enhanced group relative" data-depth="0.3">
              <a href={`/article/${ensuredArticles[4].slug}`} class="block absolute inset-0 z-30">
                <span class="sr-only">Read article: {ensuredArticles[4].title}</span>
              </a>

              <!-- Sunset-themed gradient overlay -->
              <div class="absolute inset-0 bg-gradient-to-br from-ghibli-sunset-500/8 to-ghibli-sunset-600/15 opacity-0 group-hover:opacity-100 transition-all duration-500 z-10"></div>

              <!-- Tiny floating element -->
              <div class="absolute top-2 left-2 w-3 h-3 opacity-30 group-hover:opacity-60 transition-opacity duration-300">
                <svg viewBox="0 0 24 24" fill="currentColor" class="text-ghibli-sunset-400 ghibli-float-physics" style="animation-delay: -1s;">
                  <circle cx="12" cy="12" r="10"/>
                </svg>
              </div>

              <div class="ghibli-img-container-mini">
                <img src={getImageUrl(ensuredArticles[4], 4)} alt={ensuredArticles[4].title} class="ghibli-img-enhanced" />
                <div class="ghibli-img-overlay-enhanced"></div>
              </div>

              <div class="ghibli-content-mini">
                <h3 class="ghibli-title-mini">
                  <span class="group-hover:text-ghibli-warm-700 transition-all duration-300">
                    {ensuredArticles[4].title.length > 40 ? `${ensuredArticles[4].title.substring(0, 40)}...` : ensuredArticles[4].title}
                  </span>
                </h3>

                <!-- Mini action button -->
                <div class="absolute bottom-2 right-2 w-7 h-7 rounded-full bg-white/0 group-hover:bg-white/95 flex items-center justify-center transform translate-y-3 opacity-0 group-hover:translate-y-0 group-hover:opacity-100 transition-all duration-300 shadow-sm group-hover:shadow-md">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 text-ghibli-sunset-600 transform group-hover:scale-110 transition-transform" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3" />
                  </svg>
                </div>
              </div>
            </article>

            <article class="ghibli-mini-card-enhanced-2 ghibli-glow-enhanced group relative" data-depth="0.25">
              <a href={`/article/${ensuredArticles[5].slug}`} class="block absolute inset-0 z-30">
                <span class="sr-only">Read article: {ensuredArticles[5].title}</span>
              </a>

              <!-- Earth-themed gradient overlay -->
              <div class="absolute inset-0 bg-gradient-to-br from-ghibli-earth-500/8 to-ghibli-earth-600/15 opacity-0 group-hover:opacity-100 transition-all duration-500 z-10"></div>

              <!-- Tiny floating element -->
              <div class="absolute top-2 right-2 w-3 h-3 opacity-30 group-hover:opacity-60 transition-opacity duration-300">
                <svg viewBox="0 0 24 24" fill="currentColor" class="text-ghibli-earth-400 ghibli-float-physics" style="animation-delay: -3s;">
                  <path d="M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2Z"/>
                </svg>
              </div>

              <div class="ghibli-img-container-mini">
                <img src={getImageUrl(ensuredArticles[5], 5)} alt={ensuredArticles[5].title} class="ghibli-img-enhanced" />
                <div class="ghibli-img-overlay-enhanced"></div>
              </div>

              <div class="ghibli-content-mini">
                <h3 class="ghibli-title-mini">
                  <span class="group-hover:text-ghibli-warm-700 transition-all duration-300">
                    {ensuredArticles[5].title.length > 40 ? `${ensuredArticles[5].title.substring(0, 40)}...` : ensuredArticles[5].title}
                  </span>
                </h3>

                <!-- Mini action button -->
                <div class="absolute bottom-2 right-2 w-7 h-7 rounded-full bg-white/0 group-hover:bg-white/95 flex items-center justify-center transform translate-y-3 opacity-0 group-hover:translate-y-0 group-hover:opacity-100 transition-all duration-300 shadow-sm group-hover:shadow-md">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 text-ghibli-earth-600 transform group-hover:scale-110 transition-transform" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3" />
                  </svg>
                </div>
              </div>
            </article>
          </div>
      
          <!-- Enhanced Decorative Elements -->
          <div class="ghibli-deco-enhanced-1 absolute top-[10%] left-[5%] w-16 h-16 opacity-20 ghibli-float-physics">
            <svg viewBox="0 0 100 100" fill="none" class="w-full h-full">
              <circle cx="50" cy="50" r="40" fill="url(#decorGradient1)" opacity="0.6"/>
              <circle cx="50" cy="50" r="25" fill="url(#decorGradient2)" opacity="0.4"/>
              <defs>
                <linearGradient id="decorGradient1" x1="0%" y1="0%" x2="100%" y2="100%">
                  <stop offset="0%" style="stop-color:#8e9b5e;stop-opacity:0.8" />
                  <stop offset="100%" style="stop-color:#e4a672;stop-opacity:0.6" />
                </linearGradient>
                <linearGradient id="decorGradient2" x1="0%" y1="0%" x2="100%" y2="100%">
                  <stop offset="0%" style="stop-color:#d8745e;stop-opacity:0.7" />
                  <stop offset="100%" style="stop-color:#b1b479;stop-opacity:0.5" />
                </linearGradient>
              </defs>
            </svg>
          </div>

          <div class="ghibli-deco-enhanced-2 absolute bottom-[20%] right-[8%] w-12 h-12 opacity-25 ghibli-float-physics" style="animation-delay: -5s;">
            <svg viewBox="0 0 100 100" fill="none" class="w-full h-full">
              <path d="M50,10 L60,40 L90,50 L60,60 L50,90 L40,60 L10,50 L40,40 Z" fill="url(#decorGradient3)" opacity="0.7"/>
              <defs>
                <linearGradient id="decorGradient3" x1="0%" y1="0%" x2="100%" y2="100%">
                  <stop offset="0%" style="stop-color:#867357;stop-opacity:0.8" />
                  <stop offset="100%" style="stop-color:#d8745e;stop-opacity:0.6" />
                </linearGradient>
              </defs>
            </svg>
          </div>

          <div class="ghibli-deco-enhanced-3 absolute top-[30%] right-[15%] w-10 h-10 opacity-30 ghibli-float-physics" style="animation-delay: -8s;">
            <svg viewBox="0 0 100 100" fill="none" class="w-full h-full">
              <polygon points="50,5 61,35 91,35 68,57 79,91 50,70 21,91 32,57 9,35 39,35" fill="url(#decorGradient4)" opacity="0.6"/>
              <defs>
                <linearGradient id="decorGradient4" x1="0%" y1="0%" x2="100%" y2="100%">
                  <stop offset="0%" style="stop-color:#b1b479;stop-opacity:0.9" />
                  <stop offset="100%" style="stop-color:#8e9b5e;stop-opacity:0.7" />
                </linearGradient>
              </defs>
            </svg>
          </div>

          <!-- Enhanced Dynamic Sun/Moon with time-based styling -->
          <div class={`celestial-body absolute ${isDaytime ? 'sun-enhanced' : isGoldenHour ? 'golden-sun' : 'moon-enhanced'}`}>
            <div class="celestial-glow"></div>
            <div class="celestial-core"></div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Enhanced Mountain Silhouette with Parallax -->
  <div class="mountain-silhouette-enhanced w-full absolute bottom-0 left-0 right-0 ghibli-parallax-layer" style="transform: translateZ(-0.2px) scale(1.2);">
    <svg class="w-full h-full" viewBox="0 0 1000 200" preserveAspectRatio="none">
      <defs>
        <linearGradient id="mountainGradient" x1="0%" y1="0%" x2="0%" y2="100%">
          <stop offset="0%" style={`stop-color:${currentColors.accent};stop-opacity:0.8`} />
          <stop offset="100%" style={`stop-color:${currentColors.accent};stop-opacity:0.6`} />
        </linearGradient>
      </defs>
      <path d="M0,200 L0,120 Q100,100 200,110 T400,95 T600,105 T800,90 T1000,100 L1000,200 Z" fill="url(#mountainGradient)"/>
      <path d="M0,200 L0,140 Q150,120 300,130 T600,115 T900,125 L1000,130 L1000,200 Z" fill="url(#mountainGradient)" opacity="0.7"/>
    </svg>
  </div>

  <!-- Enhanced Scroll Indicator -->
  <div class="scroll-indicator-enhanced absolute bottom-8 left-1/2 transform -translate-x-1/2 flex flex-col items-center text-white/80 z-20">
    <div class="scroll-mouse mb-2">
      <div class="scroll-wheel"></div>
    </div>
    <span class="text-xs font-medium tracking-wide">Scroll to explore</span>
  </div>
</div>

<style>
  /* Enhanced Hero Canvas with Dynamic Effects */
  .hero-canvas-enhanced {
    height: 100vh;
    min-height: 100vh;
    max-height: 100vh;
    perspective: 1500px;
    padding: 0;
    margin-top: -2rem;
    display: flex;
    flex-direction: column;
    justify-content: center;
    overflow: hidden;
    position: relative;
    transition: all 0.3s ease;
  }

  /* Time-based dynamic backgrounds */
  .hero-canvas-enhanced[data-time-of-day="day"] {
    background: linear-gradient(135deg,
      rgba(240, 244, 255, 0.95) 0%,
      rgba(232, 241, 255, 0.9) 30%,
      rgba(248, 250, 252, 0.95) 70%,
      rgba(254, 252, 232, 0.9) 100%);
  }

  .hero-canvas-enhanced[data-time-of-day="golden"] {
    background: linear-gradient(135deg,
      rgba(255, 248, 220, 0.95) 0%,
      rgba(255, 239, 195, 0.9) 30%,
      rgba(254, 215, 170, 0.85) 70%,
      rgba(251, 191, 36, 0.8) 100%);
  }

  .hero-canvas-enhanced[data-time-of-day="night"] {
    background: linear-gradient(135deg,
      rgba(30, 41, 59, 0.95) 0%,
      rgba(51, 65, 85, 0.9) 30%,
      rgba(71, 85, 105, 0.85) 70%,
      rgba(100, 116, 139, 0.8) 100%);
  }
  
  /* Enhanced Celestial Bodies */
  .celestial-body {
    top: 8%;
    right: 12%;
    width: 120px;
    height: 120px;
    z-index: 3;
    position: relative;
  }

  .sun-enhanced .celestial-core {
    width: 100%;
    height: 100%;
    background: radial-gradient(circle, #fff6e5 10%, #ffefc3 40%, #ffdb70 70%, rgba(255,219,112,0) 100%);
    border-radius: 50%;
    animation: float 25s ease-in-out infinite, sunPulse 8s ease-in-out infinite;
  }

  .sun-enhanced .celestial-glow {
    position: absolute;
    top: -20px;
    left: -20px;
    right: -20px;
    bottom: -20px;
    background: radial-gradient(circle, rgba(255,239,195,0.4) 0%, rgba(255,239,195,0.2) 50%, transparent 80%);
    border-radius: 50%;
    animation: glowPulse 6s ease-in-out infinite;
  }

  .golden-sun .celestial-core {
    width: 100%;
    height: 100%;
    background: radial-gradient(circle, #fbbf24 10%, #f59e0b 40%, #d97706 70%, rgba(217,119,6,0) 100%);
    border-radius: 50%;
    animation: float 20s ease-in-out infinite, goldenPulse 10s ease-in-out infinite;
  }

  .golden-sun .celestial-glow {
    position: absolute;
    top: -25px;
    left: -25px;
    right: -25px;
    bottom: -25px;
    background: radial-gradient(circle, rgba(251,191,36,0.5) 0%, rgba(245,158,11,0.3) 50%, transparent 80%);
    border-radius: 50%;
    animation: glowPulse 4s ease-in-out infinite;
  }

  .moon-enhanced .celestial-core {
    width: 100%;
    height: 100%;
    background: radial-gradient(circle, #f8fafc 15%, #e2e8f0 45%, #cbd5e1 75%, rgba(203,213,225,0) 100%);
    border-radius: 50%;
    animation: float 30s ease-in-out infinite, moonGlow 12s ease-in-out infinite;
    position: relative;
  }

  .moon-enhanced .celestial-core::before {
    content: '';
    position: absolute;
    top: 20%;
    left: 25%;
    width: 15px;
    height: 15px;
    background: rgba(148, 163, 184, 0.3);
    border-radius: 50%;
  }

  .moon-enhanced .celestial-core::after {
    content: '';
    position: absolute;
    top: 60%;
    left: 60%;
    width: 8px;
    height: 8px;
    background: rgba(148, 163, 184, 0.2);
    border-radius: 50%;
  }

  .moon-enhanced .celestial-glow {
    position: absolute;
    top: -15px;
    left: -15px;
    right: -15px;
    bottom: -15px;
    background: radial-gradient(circle, rgba(248,250,252,0.3) 0%, rgba(226,232,240,0.2) 50%, transparent 80%);
    border-radius: 50%;
    animation: glowPulse 8s ease-in-out infinite;
  }

  @keyframes sunPulse {
    0%, 100% { transform: scale(1); filter: brightness(1); }
    50% { transform: scale(1.05); filter: brightness(1.1); }
  }

  @keyframes goldenPulse {
    0%, 100% { transform: scale(1); filter: brightness(1) hue-rotate(0deg); }
    50% { transform: scale(1.08); filter: brightness(1.2) hue-rotate(5deg); }
  }

  @keyframes moonGlow {
    0%, 100% { transform: scale(1); filter: brightness(1); }
    50% { transform: scale(1.03); filter: brightness(1.15); }
  }
  
  /* Enhanced Mountain Silhouette */
  .mountain-silhouette-enhanced {
    height: 35%;
    opacity: 0.8;
    transition: opacity 0.3s ease;
  }

  /* Enhanced Scene Layout with Better Spacing */
  .ghibli-scene-enhanced {
    display: grid;
    grid-template-columns: repeat(12, 1fr);
    grid-template-rows: repeat(3, minmax(auto, 1fr));
    grid-template-areas:
      "main main main main main main main main secondary1 secondary1 secondary1 secondary1"
      "main main main main main main main main secondary2 secondary2 secondary2 secondary2"
      "horizontal horizontal horizontal horizontal horizontal horizontal horizontal mini mini mini mini mini";
    gap: 1rem;
    position: relative;
    width: 100%;
    height: 100%;
    max-height: calc(100vh - 40px);
    max-width: 1400px;
    margin: 0 auto;
    padding: 1rem;
    box-sizing: border-box;
    align-content: center;
    justify-content: center;
    perspective: 1000px;
  }
  
  /* Enhanced Card Base Styles */
  .ghibli-main-card-enhanced,
  .ghibli-secondary-card-enhanced-1,
  .ghibli-secondary-card-enhanced-2,
  .ghibli-horizontal-card-enhanced,
  .ghibli-mini-card-enhanced-1,
  .ghibli-mini-card-enhanced-2 {
    position: relative;
    border-radius: 1.5rem;
    overflow: hidden;
    transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow:
      0 10px 25px -5px rgba(0, 0, 0, 0.1),
      0 10px 10px -5px rgba(0, 0, 0, 0.04);
    background: linear-gradient(135deg,
      rgba(255, 255, 255, 0.95) 0%,
      rgba(255, 255, 255, 0.9) 100%);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    width: 100%;
    height: 100%;
    margin: 0;
    transform-style: preserve-3d;
  }

  /* Enhanced hover effects */
  .ghibli-main-card-enhanced:hover,
  .ghibli-secondary-card-enhanced-1:hover,
  .ghibli-secondary-card-enhanced-2:hover,
  .ghibli-horizontal-card-enhanced:hover,
  .ghibli-mini-card-enhanced-1:hover,
  .ghibli-mini-card-enhanced-2:hover {
    transform: translateY(-8px) rotateX(5deg);
    box-shadow:
      0 25px 50px -12px rgba(0, 0, 0, 0.15),
      0 25px 25px -12px rgba(0, 0, 0, 0.08);
  }
  
  /* Enhanced Image Containers */
  .ghibli-img-container-enhanced {
    position: relative;
    overflow: hidden;
    height: 65%;
    width: 100%;
    border-radius: 1.5rem 1.5rem 0 0;
  }

  .ghibli-img-container-horizontal {
    position: absolute;
    top: 0;
    left: 0;
    width: 45%;
    height: 100%;
    border-radius: 1.5rem 0 0 1.5rem;
    overflow: hidden;
  }

  .ghibli-img-container-mini {
    position: relative;
    overflow: hidden;
    height: 60%;
    width: 100%;
    border-radius: 1.5rem 1.5rem 0 0;
  }

  /* Enhanced Image Styling */
  .ghibli-img-enhanced {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.8s cubic-bezier(0.4, 0, 0.2, 1);
    display: block;
    filter: brightness(1.05) contrast(1.1) saturate(1.1);
  }

  .ghibli-img-enhanced:hover {
    transform: scale(1.08) rotate(1deg);
    filter: brightness(1.1) contrast(1.15) saturate(1.2);
  }

  /* Enhanced Image Overlays */
  .ghibli-img-overlay-enhanced {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 100%;
    background: linear-gradient(
      to top,
      rgba(0,0,0,0.6) 0%,
      rgba(0,0,0,0.3) 30%,
      rgba(0,0,0,0.1) 60%,
      rgba(0,0,0,0) 100%
    );
    transition: opacity 0.3s ease;
  }

  /* Enhanced Content Styling */
  .ghibli-content-enhanced {
    padding: 1.5rem;
    position: relative;
    background: linear-gradient(135deg,
      rgba(255, 253, 248, 0.98) 0%,
      rgba(255, 251, 240, 0.95) 100%);
    backdrop-filter: blur(10px);
    height: 35%;
    display: flex;
    flex-direction: column;
    border-radius: 0 0 1.5rem 1.5rem;
  }

  .ghibli-content-enhanced.secondary {
    height: 40%;
    padding: 1.25rem;
  }

  .ghibli-content-horizontal {
    margin-left: 45%;
    height: 100%;
    width: 55%;
    padding: 1.25rem;
    background: linear-gradient(135deg,
      rgba(255, 253, 248, 0.98) 0%,
      rgba(255, 251, 240, 0.95) 100%);
    backdrop-filter: blur(10px);
    display: flex;
    flex-direction: column;
    justify-content: center;
    border-radius: 0 1.5rem 1.5rem 0;
  }

  .ghibli-content-mini {
    padding: 1rem;
    position: relative;
    background: linear-gradient(135deg,
      rgba(255, 253, 248, 0.98) 0%,
      rgba(255, 251, 240, 0.95) 100%);
    backdrop-filter: blur(10px);
    height: 40%;
    display: flex;
    flex-direction: column;
    border-radius: 0 0 1.5rem 1.5rem;
  }
  
  /* Enhanced Typography */
  .ghibli-title-enhanced {
    font-size: clamp(1.5rem, 4vw, 2rem);
    line-height: 1.3;
    margin-bottom: 1rem;
    font-weight: 700;
    color: #422006;
    font-family: 'Inter', system-ui, sans-serif;
    letter-spacing: -0.025em;
  }

  .ghibli-title-secondary {
    font-size: clamp(1.1rem, 2.5vw, 1.4rem);
    line-height: 1.4;
    margin-bottom: 0.75rem;
    font-weight: 600;
    color: #422006;
    font-family: 'Inter', system-ui, sans-serif;
    letter-spacing: -0.02em;
  }

  .ghibli-title-horizontal {
    font-size: clamp(1rem, 2vw, 1.25rem);
    line-height: 1.4;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: #422006;
    font-family: 'Inter', system-ui, sans-serif;
  }

  .ghibli-title-mini {
    font-size: clamp(0.875rem, 1.5vw, 1rem);
    line-height: 1.4;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: #422006;
    font-family: 'Inter', system-ui, sans-serif;
  }

  .ghibli-description-enhanced {
    font-size: clamp(1rem, 1.8vw, 1.125rem);
    line-height: 1.6;
    color: #713f12;
    opacity: 0.9;
    flex-grow: 1;
  }

  .ghibli-description-secondary {
    font-size: clamp(0.875rem, 1.5vw, 1rem);
    line-height: 1.5;
    color: #713f12;
    opacity: 0.85;
    flex-grow: 1;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
  
  /* Enhanced Date Styling */
  .ghibli-date-enhanced {
    display: inline-flex;
    align-items: center;
    font-size: 0.875rem;
    color: #78716c;
    font-weight: 500;
    opacity: 0.8;
  }

  .ghibli-date-horizontal {
    display: inline-flex;
    align-items: center;
    font-size: 0.75rem;
    color: #78716c;
    font-weight: 500;
    opacity: 0.8;
  }

  /* Enhanced Button Styling */
  .ghibli-button-enhanced {
    display: inline-flex;
    align-items: center;
    padding: 0.75rem 1.5rem;
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    color: white;
    border-radius: 2rem;
    font-size: 0.875rem;
    font-weight: 600;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow:
      0 4px 6px -1px rgba(0, 0, 0, 0.1),
      0 2px 4px -1px rgba(0, 0, 0, 0.06);
    cursor: pointer;
    border: none;
    text-decoration: none;
  }

  .ghibli-button-enhanced:hover {
    background: linear-gradient(135deg, #d97706 0%, #b45309 100%);
    box-shadow:
      0 10px 15px -3px rgba(0, 0, 0, 0.1),
      0 4px 6px -2px rgba(0, 0, 0, 0.05);
    transform: translateY(-2px);
  }

  /* Enhanced Category Badges */
  .ghibli-category-enhanced {
    position: absolute;
    top: 1.25rem;
    left: 1.25rem;
    padding: 0.5rem 1rem;
    border-radius: 2rem;
    font-size: 0.75rem;
    font-weight: 600;
    background: linear-gradient(135deg,
      rgba(255, 255, 255, 0.95) 0%,
      rgba(255, 255, 255, 0.9) 100%);
    backdrop-filter: blur(10px);
    color: #422006;
    box-shadow:
      0 4px 6px -1px rgba(0, 0, 0, 0.1),
      0 2px 4px -1px rgba(0, 0, 0, 0.06);
    z-index: 10;
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  .ghibli-category-enhanced.secondary {
    background: linear-gradient(135deg,
      rgba(142, 155, 94, 0.9) 0%,
      rgba(142, 155, 94, 0.8) 100%);
    color: white;
  }

  .ghibli-category-enhanced.warm {
    background: linear-gradient(135deg,
      rgba(228, 166, 114, 0.9) 0%,
      rgba(228, 166, 114, 0.8) 100%);
    color: white;
  }

  .ghibli-category-enhanced:hover {
    transform: translateY(-2px);
    box-shadow:
      0 8px 12px -2px rgba(0, 0, 0, 0.15),
      0 4px 6px -1px rgba(0, 0, 0, 0.08);
  }

  /* Enhanced Mini Cards Grid */
  .ghibli-mini-cards-enhanced {
    grid-area: mini;
    width: 100%;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    height: 100%;
    position: relative;
    z-index: 4;
  }
  
  /* Enhanced Card Grid Areas */
  .ghibli-main-card-enhanced {
    grid-area: main;
    z-index: 5;
    min-height: min(500px, 55vh);
    max-height: 65vh;
  }

  .ghibli-secondary-card-enhanced-1 {
    grid-area: secondary1;
    z-index: 4;
    min-height: min(260px, 28vh);
    max-height: 32vh;
  }

  .ghibli-secondary-card-enhanced-2 {
    grid-area: secondary2;
    z-index: 4;
    min-height: min(260px, 28vh);
    max-height: 32vh;
  }

  .ghibli-horizontal-card-enhanced {
    grid-area: horizontal;
    z-index: 5;
    min-height: min(180px, 22vh);
    max-height: 28vh;
  }

  .ghibli-mini-card-enhanced-1,
  .ghibli-mini-card-enhanced-2 {
    min-height: min(180px, 22vh);
    max-height: 28vh;
  }

  /* Enhanced Scroll Indicator */
  .scroll-indicator-enhanced {
    animation: bounce 2s infinite;
  }

  .scroll-mouse {
    width: 24px;
    height: 40px;
    border: 2px solid rgba(255, 255, 255, 0.6);
    border-radius: 12px;
    position: relative;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
  }

  .scroll-wheel {
    width: 3px;
    height: 6px;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 2px;
    position: absolute;
    top: 6px;
    left: 50%;
    transform: translateX(-50%);
    animation: scrollWheel 2s infinite;
  }

  @keyframes scrollWheel {
    0% {
      opacity: 1;
      transform: translateX(-50%) translateY(0);
    }
    50% {
      opacity: 1;
      transform: translateX(-50%) translateY(8px);
    }
    100% {
      opacity: 0;
      transform: translateX(-50%) translateY(16px);
    }
  }

  @keyframes bounce {
    0%, 20%, 53%, 80%, 100% {
      transform: translate(-50%, 0);
    }
    40%, 43% {
      transform: translate(-50%, -8px);
    }
    70% {
      transform: translate(-50%, -4px);
    }
    90% {
      transform: translate(-50%, -2px);
    }
  }
  
  /* Responsive Design */
  @media (max-width: 1200px) {
    .ghibli-scene-enhanced {
      max-width: 1200px;
      gap: 0.75rem;
    }
  }

  @media (max-width: 1024px) {
    .ghibli-scene-enhanced {
      grid-template-areas:
        "main main main main main main main main main main main main"
        "secondary1 secondary1 secondary1 secondary1 secondary1 secondary1 secondary2 secondary2 secondary2 secondary2 secondary2 secondary2"
        "horizontal horizontal horizontal horizontal horizontal horizontal horizontal horizontal horizontal horizontal horizontal horizontal"
        "mini mini mini mini mini mini mini mini mini mini mini mini";
      gap: 0.75rem;
    }

    .ghibli-main-card-enhanced {
      min-height: min(400px, 45vh);
      max-height: 50vh;
    }

    .ghibli-secondary-card-enhanced-1,
    .ghibli-secondary-card-enhanced-2 {
      min-height: min(220px, 25vh);
      max-height: 30vh;
    }
  }

  @media (max-width: 768px) {
    .hero-canvas-enhanced {
      overflow-y: auto;
      height: auto;
      min-height: 100vh;
    }

    .ghibli-scene-enhanced {
      display: flex;
      flex-direction: column;
      gap: 1rem;
      padding: 1rem 0.5rem;
      max-height: none;
    }

    .ghibli-main-card-enhanced {
      min-height: 300px;
      max-height: 400px;
    }

    .ghibli-secondary-card-enhanced-1,
    .ghibli-secondary-card-enhanced-2,
    .ghibli-horizontal-card-enhanced {
      min-height: 200px;
      max-height: 250px;
    }

    .ghibli-mini-card-enhanced-1,
    .ghibli-mini-card-enhanced-2 {
      min-height: 160px;
      max-height: 200px;
    }

    .ghibli-mini-cards-enhanced {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 0.75rem;
    }

    .ghibli-img-container-horizontal {
      position: relative;
      width: 100%;
      height: 50%;
      border-radius: 1.5rem 1.5rem 0 0;
    }

    .ghibli-content-horizontal {
      margin-left: 0;
      width: 100%;
      height: 50%;
      border-radius: 0 0 1.5rem 1.5rem;
    }

    .celestial-body {
      width: 80px;
      height: 80px;
      top: 5%;
      right: 8%;
    }
  }
  
  /* Responsive layout fixes */
  @media (max-width: 1024px) {
    .ghibli-scene {
      grid-template-areas: 
        "main main main main main main main main main main main main"
        "secondary1 secondary1 secondary1 secondary1 secondary1 secondary1 secondary2 secondary2 secondary2 secondary2 secondary2 secondary2"
        "horizontal horizontal horizontal horizontal horizontal horizontal horizontal horizontal horizontal horizontal horizontal horizontal"
        "mini mini mini mini mini mini mini mini mini mini mini mini";
      gap: 0.5rem;
    }
  }
  
  @media (max-width: 768px) {
    .ghibli-scene {
      display: flex;
      flex-direction: column;
      gap: 0.5rem;
      padding: 0.5rem 0;
    }
  }
  
  /* Content scaling */
  .ghibli-content h2 {
    font-size: clamp(1.2rem, 3vw, 1.5rem);
    line-height: 1.4;
    margin-bottom: 0.75rem;
    font-weight: 700;
    color: #422006;
  }

  .ghibli-content h3 {
    font-size: clamp(1rem, 2vw, 1.2rem);
    line-height: 1.4;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: #422006;
  }

  .ghibli-content p {
    font-size: clamp(0.875rem, 1.5vw, 1rem);
    line-height: 1.5;
    color: #713f12;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
  
  /* Category badges */
  .ghibli-category {
    position: absolute;
    top: 1rem;
    left: 1rem;
    padding: 0.35rem 0.75rem;
    border-radius: 2rem;
    font-size: 0.75rem;
    font-weight: 600;
    background-color: rgba(255, 255, 255, 0.85);
    color: #422006;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.15);
    z-index: 10;
    transition: all 0.3s ease;
  }
  
  article:hover .ghibli-category {
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
  }
  
  /* Main card special styling */
  .ghibli-main-card::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg, #8e9b5e, #e4a672, #d8745e, #b1b479, #867357);
    border-radius: 0.85rem;
    z-index: -1;
    opacity: 0;
    transition: opacity 0.6s ease;
  }
  
  .ghibli-main-card:hover::before {
    opacity: 0.6;
  }
  
  /* Secondary card special styling */
  .ghibli-secondary-card-1::after,
  .ghibli-secondary-card-2::after {
    content: '';
    position: absolute;
    right: 1rem;
    top: 30%;
    width: 20px;
    height: 20px;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'%3E%3Cpath d='M50,10 L60,40 L90,50 L60,60 L50,90 L40,60 L10,50 L40,40 Z' fill='%23f3f4f6' fill-opacity='0.2'/%3E%3C/svg%3E");
    background-size: contain;
    opacity: 0;
    transition: opacity 0.4s ease, transform 0.4s ease;
    transform: translateY(10px);
  }
  
  .ghibli-secondary-card-1:hover::after,
  .ghibli-secondary-card-2:hover::after {
    opacity: 1;
    transform: translateY(0);
  }
  
  /* Horizontal card special styling */
  .ghibli-horizontal-card::after {
    content: '';
    position: absolute;
    bottom: 1rem;
    right: 1rem;
    width: 16px;
    height: 16px;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'%3E%3Ccircle cx='50' cy='50' r='40' fill='%23f3f4f6' fill-opacity='0.2'/%3E%3C/svg%3E");
    background-size: contain;
    opacity: 0;
    transition: opacity 0.4s ease, transform 0.4s ease;
    transform: translateY(10px);
  }
  
  .ghibli-horizontal-card:hover::after {
    opacity: 1;
    transform: translateY(0);
  }
  
  /* Mini cards special styling */
  .ghibli-mini-card-1::before,
  .ghibli-mini-card-2::before {
    content: '';
    position: absolute;
    bottom: 0;
    right: 0;
    width: 40px;
    height: 40px;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'%3E%3Cpath d='M100,0 L100,100 L0,100 Z' fill='%23f3f4f6' fill-opacity='0.1'/%3E%3C/svg%3E");
    background-size: contain;
    z-index: 2;
  }
  
  /* Date styling */
  .ghibli-date {
    display: inline-flex;
    align-items: center;
    font-size: 0.8rem;
    color: #78716c;
  }
  
  /* Button styling */
  .ghibli-button {
    display: inline-flex;
    align-items: center;
    padding: 0.5rem 1rem;
    background: linear-gradient(to right, #f59e0b, #d97706);
    color: white;
    border-radius: 2rem;
    font-size: 0.875rem;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    cursor: pointer;
  }
  
  article:hover .ghibli-button {
    transform: translateY(-2px);
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  }
  
  /* Decorative elements */
  .ghibli-deco-1 {
    position: absolute;
    top: 5%;
    left: 3%;
    width: 60px;
    height: 60px;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'%3E%3Cpath d='M50,10 L60,40 L90,50 L60,60 L50,90 L40,60 L10,50 L40,40 Z' fill='%238e9b5e' fill-opacity='0.4'/%3E%3C/svg%3E");
    background-size: contain;
    animation: float 15s ease-in-out infinite;
    pointer-events: none;
    z-index: 2;
  }
  
  .ghibli-deco-2 {
    position: absolute;
    bottom: 15%;
    right: 10%;
    width: 45px;
    height: 45px;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'%3E%3Ccircle cx='50' cy='50' r='40' fill='%23e4a672' fill-opacity='0.4'/%3E%3C/svg%3E");
    background-size: contain;
    animation: float 18s ease-in-out infinite;
    pointer-events: none;
    z-index: 2;
  }
  
  .ghibli-deco-3 {
    position: absolute;
    top: 25%;
    right: 20%;
    width: 50px;
    height: 50px;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'%3E%3Cpath d='M30,20 Q50,0 70,20 T100,50 Q80,80 50,80 Q20,80 0,50 Q20,20 30,20 Z' fill='%23d8745e' fill-opacity='0.3'/%3E%3C/svg%3E");
    background-size: contain;
    animation: float 20s ease-in-out infinite;
    pointer-events: none;
    z-index: 2;
  }
  
  /* Animation for floating elements */
  @keyframes float {
    0% { transform: translateY(0) rotate(0); }
    50% { transform: translateY(-15px) rotate(2deg); }
    100% { transform: translateY(0) rotate(0); }
  }
  
  /* Add scroll indicator to guide users */
  .scroll-indicator {
    position: absolute;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    width: 30px;
    height: 50px;
    border: 2px solid rgba(255, 255, 255, 0.5);
    border-radius: 15px;
    display: flex;
    justify-content: center;
    padding-top: 10px;
    z-index: 20;
  }

  .scroll-indicator::before {
    content: '';
    width: 6px;
    height: 6px;
    background: white;
    border-radius: 50%;
    animation: scrollDown 2s infinite;
  }

  @keyframes scrollDown {
    0% { transform: translateY(0); opacity: 0; }
    30% { opacity: 1; }
    60% { opacity: 1; }
    100% { transform: translateY(15px); opacity: 0; }
  }

  /* Card hover effects */
  .ghibli-main-card:hover,
  .ghibli-secondary-card-1:hover,
  .ghibli-secondary-card-2:hover,
  .ghibli-horizontal-card:hover,
  .ghibli-mini-card-1:hover,
  .ghibli-mini-card-2:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px -10px rgba(0, 0, 0, 0.15);
  }

  /* Mobile specific adjustments */
  @media (max-width: 768px) {
    .hero-canvas {
      overflow-y: auto;
    }
    
    .ghibli-main-card {
      min-height: min(250px, 30vh);
      max-height: 40vh;
    }
    
    .ghibli-secondary-card-1,
    .ghibli-secondary-card-2,
    .ghibli-horizontal-card {
      min-height: min(160px, 20vh);
      max-height: 25vh;
    }
    
    .ghibli-mini-card-1,
    .ghibli-mini-card-2 {
      min-height: min(120px, 15vh);
      max-height: 20vh;
    }
    
    .ghibli-horizontal-card .ghibli-img-container {
      position: relative;
      width: 100%;
      height: 50%;
    }
    
    .ghibli-horizontal-card .ghibli-content {
      margin-left: 0;
      width: 100%;
      height: 50%;
    }
  }
</style>

<script>
  // Enhanced Hero Interactions
  document.addEventListener('DOMContentLoaded', () => {
    const heroCanvas = document.querySelector('.hero-canvas-enhanced');
    const ghibliScene = document.querySelector('.ghibli-scene-enhanced');

    // Viewport height adjustment
    function adjustHeight() {
      if (heroCanvas) {
        const vh = window.innerHeight;
        heroCanvas.style.height = `${vh}px`;

        if (ghibliScene) {
          ghibliScene.style.maxHeight = `${vh - 60}px`;
        }
      }
    }

    adjustHeight();
    window.addEventListener('resize', adjustHeight);

    // Enhanced mouse tracking for glow effects
    if (heroCanvas) {
      heroCanvas.addEventListener('mousemove', (e) => {
        const rect = heroCanvas.getBoundingClientRect();
        const x = ((e.clientX - rect.left) / rect.width) * 100;
        const y = ((e.clientY - rect.top) / rect.height) * 100;

        // Update CSS custom properties for mouse position
        heroCanvas.style.setProperty('--mouse-x', `${x}%`);
        heroCanvas.style.setProperty('--mouse-y', `${y}%`);

        // Enhanced parallax effect
        const cards = document.querySelectorAll('[data-depth]');
        const mouseX = (e.clientX / window.innerWidth - 0.5) * 2;
        const mouseY = (e.clientY / window.innerHeight - 0.5) * 2;

        cards.forEach(card => {
          const depth = parseFloat(card.getAttribute('data-depth') || '0');
          const maxMove = 15;
          const translateX = Math.max(-maxMove, Math.min(maxMove, mouseX * depth * 30));
          const translateY = Math.max(-maxMove, Math.min(maxMove, mouseY * depth * 30));
          const rotateX = Math.max(-3, Math.min(3, mouseY * depth * 3));
          const rotateY = Math.max(-3, Math.min(3, mouseX * depth * 3));

          (card as HTMLElement).style.transform =
            `translateX(${translateX}px) translateY(${translateY}px) rotateX(${rotateX}deg) rotateY(${rotateY}deg)`;
        });
      });

      // Reset transforms when mouse leaves
      heroCanvas.addEventListener('mouseleave', () => {
        const cards = document.querySelectorAll('[data-depth]');
        cards.forEach(card => {
          (card as HTMLElement).style.transform = 'translateX(0) translateY(0) rotateX(0) rotateY(0)';
        });
      });
    }

    // Intersection Observer for scroll animations
    const observerOptions = {
      threshold: 0.1,
      rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          entry.target.classList.add('animate-in');
        }
      });
    }, observerOptions);

    // Observe all cards
    const cards = document.querySelectorAll('[class*="ghibli-"][class*="card"]');
    cards.forEach(card => observer.observe(card));

    // Performance optimization: throttle mouse events
    let ticking = false;
    function requestTick() {
      if (!ticking) {
        requestAnimationFrame(() => {
          ticking = false;
        });
        ticking = true;
      }
    }

    // Add loading animation
    setTimeout(() => {
      if (ghibliScene) {
        ghibliScene.classList.add('loaded');
      }
    }, 100);
  });
</script>