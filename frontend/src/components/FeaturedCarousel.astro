---
// FeaturedCarousel.astro - A component to showcase featured articles in a carousel
import { siteConfig } from '../config/site';
import { ArticleDetailResp } from '../lib/api';
const { containerWidth } = siteConfig;

interface Props {
  articles: ArticleDetailResp[];
}

const { articles = [] } = Astro.props;

// Ensure we have at least empty slides if no articles are provided
const ensuredArticles = articles.length > 0 ? articles : [];

// Helper function for formatting dates
function formatDate(dateString: string): string {
  try {
    const options: Intl.DateTimeFormatOptions = { year: 'numeric', month: 'short', day: 'numeric' };
    return new Date(dateString).toLocaleDateString('en-US', options);
  } catch (e) {
    return 'Recent';
  }
}

// Helper function to get image URL with fallback
function getImageUrl(article: ArticleDetailResp, index: number): string {
  return article.cover_image || 
         article.featured_image || 
         `/images/placeholder-${(index % 3) + 1}.jpg`;
}

// Helper function to get category name with fallback
function getCategoryName(article: ArticleDetailResp): string {
  return article.category?.name || "Uncategorized";
}
---

<section class="py-20 relative overflow-hidden ghibli-crystal-atmosphere" style="background: linear-gradient(135deg, rgba(248, 250, 252, 0.95) 0%, rgba(255, 255, 255, 0.9) 50%, rgba(254, 252, 232, 0.95) 100%);">
  <!-- Ultra-Enhanced 2025 Ghibli Background Effects -->
  <div class="absolute inset-0 opacity-20">
    <div class="absolute top-0 right-0 w-[500px] h-[500px] bg-gradient-radial from-orange-200/30 to-transparent blur-3xl animate-pulse"></div>
    <div class="absolute bottom-0 left-0 w-[400px] h-[400px] bg-gradient-radial from-yellow-200/25 to-transparent blur-3xl animate-pulse" style="animation-delay: -3s;"></div>
    <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[600px] h-[300px] bg-gradient-radial from-green-200/20 to-transparent blur-3xl animate-pulse" style="animation-delay: -6s;"></div>
  </div>

  <!-- Magical Floating Particles -->
  <div class="absolute top-1/4 left-20 w-3 h-3 bg-orange-300 rounded-full opacity-60 animate-bounce" style="animation-duration: 4s;"></div>
  <div class="absolute top-1/3 right-32 w-4 h-4 bg-yellow-300 rounded-full opacity-50 animate-bounce" style="animation-duration: 6s; animation-delay: -2s;"></div>
  <div class="absolute bottom-1/4 left-1/3 w-2 h-2 bg-green-300 rounded-full opacity-70 animate-bounce" style="animation-duration: 5s; animation-delay: -4s;"></div>

  <!-- Ethereal Decorative Elements -->
  <div class="absolute top-10 right-10 opacity-15">
    <svg width="120" height="120" viewBox="0 0 200 200" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M100,10 C120,25 150,5 170,20 C190,35 195,65 180,85 C165,105 180,135 160,160 C140,185 105,190 80,170 C55,150 25,160 15,130 C5,100 25,75 40,55 C55,35 80,40 100,10 Z" stroke="#8e9b5e" stroke-width="2" fill="none"/>
    </svg>
  </div>
  
  <div class={`container mx-auto px-3 sm:px-4 ${containerWidth} relative`}>
    <div class="text-center mb-16 max-w-4xl mx-auto">
      <span class="inline-block px-6 py-2 text-sm font-semibold text-orange-800 bg-gradient-to-r from-orange-100 to-yellow-100 rounded-full mb-6 shadow-lg transform hover:-translate-y-2 transition-all duration-300 ghibli-glow-enhanced" style="font-family: var(--font-ghibli-primary);">✨ Featured Articles</span>
      <h2 class="text-4xl md:text-6xl font-bold text-gray-800 mb-8 leading-tight relative inline-block ghibli-text-glow" style="font-family: var(--font-ghibli-heading);">
        Editor's Selections
        <span class="absolute -bottom-3 left-1/2 transform -translate-x-1/2 h-2 w-32 bg-gradient-to-r from-orange-400 to-red-400 rounded-full opacity-80"></span>
      </h2>
      <p class="text-xl md:text-2xl text-gray-600 mx-auto leading-relaxed mt-8" style="font-family: var(--font-ghibli-body);">Handpicked stories to inspire, inform, and guide your next tech adventure 🌟</p>
    </div>
    
    <!-- Ultra-Enhanced Carousel with 2025 Ghibli Magic -->
    <div class="carousel-container relative">
      <!-- Magical Arrow Navigation -->
      <button class="carousel-arrow carousel-prev absolute left-4 top-1/2 transform -translate-y-1/2 z-20 bg-white/90 backdrop-blur-sm hover:bg-white text-orange-600 hover:text-orange-700 p-4 rounded-full shadow-xl hover:shadow-2xl transition-all duration-300 focus:outline-none ghibli-glow-enhanced hover:scale-110" aria-label="Previous slide" data-ghibli-interactive>
        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2.5" d="M15 19l-7-7 7-7" />
        </svg>
      </button>

      <button class="carousel-arrow carousel-next absolute right-4 top-1/2 transform -translate-y-1/2 z-20 bg-white/90 backdrop-blur-sm hover:bg-white text-orange-600 hover:text-orange-700 p-4 rounded-full shadow-xl hover:shadow-2xl transition-all duration-300 focus:outline-none ghibli-glow-enhanced hover:scale-110" aria-label="Next slide" data-ghibli-interactive>
        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2.5" d="M9 5l7 7-7 7" />
        </svg>
      </button>
      
      <!-- Carousel Track -->
      <div class="carousel-track overflow-hidden" aria-live="polite">
        <div class="carousel-slides flex transition-transform duration-500 ease-out">
          {ensuredArticles.length > 0 ? (
            ensuredArticles.map((article, index) => (
              <div class="carousel-slide flex-none w-full md:w-1/2 lg:w-1/3 px-4">
                <article class="bg-white/95 backdrop-blur-sm rounded-3xl shadow-2xl border-2 border-orange-200/50 overflow-hidden h-full flex flex-col hover:shadow-3xl transform hover:-translate-y-2 transition-all duration-500 group relative ghibli-glow-enhanced" data-ghibli-animate="fadeInUp" data-ghibli-interactive>
                  <!-- Enhanced magical glow effect -->
                  <div class="absolute -inset-1 bg-gradient-to-r from-orange-400/30 to-red-400/30 rounded-3xl opacity-0 group-hover:opacity-100 blur-lg transition-all duration-700"></div>
                  
                  <div class="relative pb-[60%] overflow-hidden">
                    <img
                      src={getImageUrl(article, index)}
                      alt={article.title}
                      class="absolute inset-0 w-full h-full object-cover transition-transform duration-1000 group-hover:scale-110"
                    />
                    <div class="absolute inset-0 bg-gradient-to-t from-black/70 via-transparent to-transparent"></div>
                    <span class="absolute top-4 left-4 bg-gradient-to-r from-orange-500 to-red-500 text-white text-sm font-semibold px-4 py-2 rounded-full shadow-lg ghibli-glow-enhanced" style="font-family: var(--font-ghibli-primary);">
                      {getCategoryName(article)}
                    </span>
                    <div class="absolute top-4 right-4 text-xl opacity-0 group-hover:opacity-100 transition-opacity duration-500">✨</div>
                  </div>
                  
                  <div class="p-8 flex-grow flex flex-col relative z-10">
                    <h3 class="text-xl sm:text-2xl font-bold mb-4 text-gray-800 group-hover:text-orange-700 transition-colors line-clamp-2 leading-tight" style="font-family: var(--font-ghibli-heading);">
                      <a href={`/article/${article.slug}`} class="hover:underline">
                        {article.title}
                      </a>
                    </h3>
                    <p class="text-gray-600 mb-6 flex-grow line-clamp-3 text-lg leading-relaxed" style="font-family: var(--font-ghibli-body);">
                      {article.description || article.excerpt || "Discover amazing insights and expert reviews in this featured article."}
                    </p>
                    <div class="flex items-center justify-between">
                      <div class="flex items-center">
                        <img
                          src={article.author?.avatar || `https://ui-avatars.com/api/?name=${encodeURIComponent(article.author?.name || 'Unknown')}&background=ff6b35&color=fff`}
                          alt={article.author?.name || "Author"}
                          class="w-10 h-10 rounded-full mr-3 border-2 border-orange-200 shadow-md"
                        />
                        <span class="font-semibold text-gray-700" style="font-family: var(--font-ghibli-accent);">{article.author?.name || "BrandReviews Team"}</span>
                      </div>
                      <div class="flex items-center text-gray-500 text-sm" style="font-family: var(--font-ghibli-accent);">
                        <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                          <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd" />
                        </svg>
                        {formatDate(article.publish_date)}
                      </div>
                    </div>
                  </div>
                </article>
              </div>
            ))
          ) : (
            <div class="carousel-slide flex-none w-full px-4">
              <div class="bg-white/90 backdrop-blur-sm rounded-3xl shadow-xl border-2 border-orange-200/50 p-16 text-center ghibli-glow-enhanced">
                <div class="mb-6">
                  <svg class="w-16 h-16 mx-auto text-orange-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                  </svg>
                </div>
                <h3 class="text-2xl font-bold text-gray-800 mb-4" style="font-family: var(--font-ghibli-heading);">No Featured Articles</h3>
                <p class="text-gray-600 text-lg" style="font-family: var(--font-ghibli-body);">Check back soon for amazing featured content! ✨</p>
              </div>
            </div>
          )}
        </div>
      </div>
      
      <!-- Carousel Indicators -->
      <div class="carousel-indicators flex justify-center mt-8 space-x-2">
        {ensuredArticles.length > 0 && Array.from({ length: Math.ceil(ensuredArticles.length / 3) }).map((_, i) => (
          <button class={`carousel-dot w-3 h-3 rounded-full bg-ghibli-earth-300 transition-all duration-300 ${i === 0 ? 'w-10 bg-ghibli-warm-400' : ''}`} aria-label={`Go to slide ${i + 1}`} data-index={i}></button>
        ))}
      </div>
    </div>
  </div>
</section>

<script>
  document.addEventListener('DOMContentLoaded', () => {
    const track = document.querySelector('.carousel-slides');
    const slides = document.querySelectorAll('.carousel-slide');
    const prevButton = document.querySelector('.carousel-prev');
    const nextButton = document.querySelector('.carousel-next');
    const dots = document.querySelectorAll('.carousel-dot');
    const slideWidth = slides.length > 0 ? slides[0].getBoundingClientRect().width : 0;
    
    if (!track || slides.length === 0 || !prevButton || !nextButton) return;
    
    let currentIndex = 0;
    let slidesPerView = 1;
    
    function updateSlidesPerView() {
      if (window.innerWidth >= 1024) {
        slidesPerView = 3; // Desktop: 3 slides
      } else if (window.innerWidth >= 768) {
        slidesPerView = 2; // Tablet: 2 slides
      } else {
        slidesPerView = 1; // Mobile: 1 slide
      }
    }
    
    function updatePosition() {
      if (track) {
        track.style.transform = `translateX(-${currentIndex * slideWidth}px)`;
        
        // Update active dot
        dots.forEach((dot, index) => {
          if (index === Math.floor(currentIndex / slidesPerView)) {
            dot.classList.add('w-10', 'bg-ghibli-warm-400');
            dot.classList.remove('bg-ghibli-earth-300');
          } else {
            dot.classList.remove('w-10', 'bg-ghibli-warm-400');
            dot.classList.add('bg-ghibli-earth-300');
          }
        });
      }
    }
    
    function nextSlide() {
      updateSlidesPerView();
      if (currentIndex < slides.length - slidesPerView) {
        currentIndex++;
        updatePosition();
      } else {
        // Loop back to the beginning with animation
        currentIndex = 0;
        updatePosition();
      }
    }
    
    function prevSlide() {
      updateSlidesPerView();
      if (currentIndex > 0) {
        currentIndex--;
        updatePosition();
      } else {
        // Loop to the end with animation
        currentIndex = slides.length - slidesPerView;
        currentIndex = Math.max(0, currentIndex); // Ensure not negative
        updatePosition();
      }
    }
    
    // Event listeners
    nextButton.addEventListener('click', nextSlide);
    prevButton.addEventListener('click', prevSlide);
    
    // Dot navigation
    dots.forEach((dot, index) => {
      dot.addEventListener('click', () => {
        updateSlidesPerView();
        currentIndex = index * slidesPerView;
        updatePosition();
      });
    });
    
    // Auto-advance slides every 6 seconds
    let autoplayInterval = setInterval(nextSlide, 6000);
    
    // Pause autoplay on hover
    const carouselContainer = document.querySelector('.carousel-container');
    if (carouselContainer) {
      carouselContainer.addEventListener('mouseenter', () => {
        clearInterval(autoplayInterval);
      });
      
      carouselContainer.addEventListener('mouseleave', () => {
        autoplayInterval = setInterval(nextSlide, 6000);
      });
    }
    
    // Initialize
    updateSlidesPerView();
    updatePosition();
    
    // Handle window resize
    window.addEventListener('resize', () => {
      updateSlidesPerView();
      updatePosition();
    });
  });
</script>

<style>
  /* 2025 Enhanced Ghibli Effects for Featured Carousel */

  /* Gradient radial utility */
  .bg-gradient-radial {
    background: radial-gradient(circle, var(--tw-gradient-stops));
  }

  /* Enhanced floating animation */
  .animate-float {
    animation: ghibliFloat 8s ease-in-out infinite;
  }

  @keyframes ghibliFloat {
    0%, 100% {
      transform: translateY(0px) rotate(0deg);
      opacity: 0.6;
    }
    25% {
      transform: translateY(-15px) rotate(2deg);
      opacity: 0.8;
    }
    50% {
      transform: translateY(-25px) rotate(0deg);
      opacity: 1;
    }
    75% {
      transform: translateY(-10px) rotate(-2deg);
      opacity: 0.8;
    }
  }

  /* Enhanced carousel slide transitions */
  .carousel-slide {
    transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .carousel-slides {
    transition: transform 0.8s cubic-bezier(0.4, 0, 0.2, 1);
  }

  /* Magical text glow effect */
  .ghibli-text-glow {
    text-shadow:
      0 0 10px rgba(255, 255, 255, 0.5),
      0 0 20px rgba(255, 193, 7, 0.3),
      0 0 30px rgba(255, 142, 83, 0.2);
  }

  /* Enhanced hover effects */
  .ghibli-glow-enhanced:hover {
    transform: translateY(-6px) scale(1.02);
    box-shadow:
      0 25px 50px rgba(0, 0, 0, 0.15),
      0 0 0 1px rgba(255, 142, 83, 0.1) inset;
  }

  /* Line clamp utilities */
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  /* Responsive adjustments */
  @media (max-width: 640px) {
    .carousel-arrow {
      display: none;
    }

    .ghibli-text-glow {
      text-shadow: none;
    }
  }

  @media (max-width: 768px) {
    .animate-float {
      animation-duration: 6s;
    }
  }

  /* Reduced motion support */
  @media (prefers-reduced-motion: reduce) {
    .animate-float,
    .carousel-slide,
    .carousel-slides {
      animation: none !important;
      transition: none !important;
    }

    .ghibli-glow-enhanced:hover {
      transform: none;
    }
  }

  /* High contrast mode */
  @media (prefers-contrast: high) {
    .ghibli-text-glow {
      text-shadow: none;
    }

    .ghibli-glow-enhanced {
      border: 2px solid currentColor;
    }
  }
</style>