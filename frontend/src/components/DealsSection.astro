---
// DealsSection.astro - A component to showcase the latest deals and offers
import { siteConfig } from '../config/site';
import { DealDetailResp } from '../lib/api';
const { containerWidth } = siteConfig;

interface Props {
  deals: DealDetailResp[];
}

const { deals = [] } = Astro.props;

// Ensure we have at least some deals to display
const ensuredDeals = deals.length > 0 ? deals : [];

// Format currency with appropriate symbols
function formatPrice(price: number | undefined): string {
  if (price === undefined || isNaN(price)) return '';
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 2
  }).format(price);
}

// Calculate discount percentage
function calculateDiscount(original: number | undefined, sale: number | undefined): string {
  if (!original || !sale || original <= 0) return '';
  const percentage = Math.round(((original - sale) / original) * 100);
  return percentage > 0 ? `${percentage}% OFF` : '';
}

// Format date to show days remaining
function formatExpiryDate(timestamp: number | undefined): string {
  if (!timestamp) return 'Limited time';
  
  const expiryDate = new Date(timestamp * 1000);
  const today = new Date();
  
  // Set hours, minutes, seconds and milliseconds to 0 for both dates to compare just the days
  today.setHours(0, 0, 0, 0);
  expiryDate.setHours(0, 0, 0, 0);
  
  // Calculate the difference in days
  const diffTime = expiryDate.getTime() - today.getTime();
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  
  if (diffDays < 0) {
    return 'Expired';
  } else if (diffDays === 0) {
    return 'Today only!';
  } else if (diffDays === 1) {
    return 'Ends tomorrow';
  } else if (diffDays <= 7) {
    return `${diffDays} days left`;
  } else {
    return `Ends ${expiryDate.toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}`;
  }
}
---

<section class="py-20 relative overflow-hidden ghibli-crystal-atmosphere" style="background: linear-gradient(135deg, rgba(216, 116, 94, 0.05) 0%, rgba(228, 166, 114, 0.08) 30%, rgba(251, 191, 36, 0.05) 70%, rgba(245, 158, 11, 0.08) 100%);">
  <!-- Ultra-Enhanced 2025 Ghibli Background Effects -->
  <div class="absolute inset-0 opacity-25">
    <div class="absolute top-0 right-0 w-[500px] h-[500px] bg-gradient-radial from-red-200/30 to-transparent blur-3xl animate-pulse"></div>
    <div class="absolute bottom-0 left-0 w-[400px] h-[400px] bg-gradient-radial from-orange-200/25 to-transparent blur-3xl animate-pulse" style="animation-delay: -3s;"></div>
    <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[600px] h-[300px] bg-gradient-radial from-yellow-200/20 to-transparent blur-3xl animate-pulse" style="animation-delay: -6s;"></div>
  </div>

  <!-- Magical Floating Elements -->
  <div class="absolute top-20 left-[10%] opacity-60">
    <div class="w-4 h-4 rounded-full bg-red-300 animate-bounce" style="animation-duration: 5s; animation-delay: -2s;"></div>
  </div>
  <div class="absolute bottom-20 right-[10%] opacity-50">
    <div class="w-3 h-3 rounded-full bg-orange-300 animate-bounce" style="animation-duration: 6s; animation-delay: -4s;"></div>
  </div>
  <div class="absolute top-1/3 right-[20%] opacity-70">
    <div class="w-2 h-2 rounded-full bg-yellow-300 animate-bounce" style="animation-duration: 4s; animation-delay: -1s;"></div>
  </div>
  
  <div class={`container mx-auto px-4 ${containerWidth} relative z-10`}>
    <div class="flex flex-col sm:flex-row justify-between items-center mb-16">
      <div class="relative mb-8 sm:mb-0">
        <span class="inline-block px-6 py-2 text-sm font-semibold text-red-800 bg-gradient-to-r from-red-100 to-orange-100 rounded-full mb-4 shadow-lg transform hover:-translate-y-1 transition-all duration-300 ghibli-glow-enhanced" style="font-family: var(--font-ghibli-primary);">🔥 Special Offers</span>
        <h2 class="text-4xl md:text-5xl font-bold text-gray-800 relative inline-block ghibli-text-glow" style="font-family: var(--font-ghibli-heading);">
          Today's Best Deals
          <span class="absolute -bottom-3 left-0 h-2 w-28 bg-gradient-to-r from-red-400 to-orange-400 rounded-full opacity-80"></span>
        </h2>

        <!-- Enhanced Decorative element -->
        <div class="absolute -left-10 -top-8 opacity-25 animate-bounce" style="animation-duration: 6s;">
          <svg width="50" height="50" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="text-red-500">
            <path d="M21 11.5C21 16.75 16.75 21 11.5 21C6.25 21 2 16.75 2 11.5C2 6.25 6.25 2 11.5 2" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
            <path d="M22 22L20 20" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
            <path d="M15 5H21V11" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <path d="M21 5L14 12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </div>
      </div>
      <a href="/deals" class="group text-red-600 hover:text-red-700 font-semibold flex items-center bg-white/90 backdrop-blur-sm px-6 py-3 rounded-2xl shadow-xl hover:shadow-2xl transition-all duration-300 hover:scale-105 ghibli-glow-enhanced" style="font-family: var(--font-ghibli-primary);" data-ghibli-interactive>
        View all deals ✨
        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-2 transform group-hover:translate-x-2 transition-transform duration-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2.5" d="M9 5l7 7-7 7" />
        </svg>
      </a>
    </div>
    
    {ensuredDeals.length > 0 ? (
      <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
        {ensuredDeals.slice(0, 8).map((deal, index) => (
          <a 
            href={deal.deal_url || `/deals/${deal.slug}`} 
            target={deal.deal_url ? "_blank" : "_self"} 
            rel={deal.deal_url ? "noopener noreferrer" : ""}
            class="bg-white rounded-xl shadow-ghibli overflow-hidden hover:shadow-ghibli-hover transform hover:-translate-y-1 transition-all duration-300 border border-ghibli-earth-100/60 group flex flex-col h-full"
          >
            <!-- Deal image with discount badge -->
            <div class="relative pt-[70%] overflow-hidden">
              <img 
                src={deal.image_url || `/images/deal-placeholder-${(index % 4) + 1}.jpg`} 
                alt={deal.title} 
                class="absolute inset-0 w-full h-full object-cover group-hover:scale-105 transition-transform duration-500"
              />
              
              {/* Overlay gradient */}
              <div class="absolute inset-0 bg-gradient-to-t from-ghibli-earth-900/40 to-transparent opacity-60"></div>
              
              {/* Discount badge */}
              {(deal.discount_value || (deal.original_price && deal.sale_price)) && (
                <span class="absolute top-3 right-3 bg-ghibli-warm-600 text-white font-bold px-3 py-1.5 rounded-full text-sm shadow-lg">
                  {deal.discount_type === 'percentage' && deal.discount_value 
                    ? `${deal.discount_value}% OFF`
                    : calculateDiscount(deal.original_price, deal.sale_price)}
                </span>
              )}
              
              {/* Store badge */}
              {deal.store && (
                <span class="absolute bottom-3 left-3 bg-white/90 backdrop-blur-md text-ghibli-forest-700 text-xs px-2.5 py-1 rounded-full font-medium">
                  {deal.store}
                </span>
              )}
              
              {/* Timer badge for ending soon deals */}
              {deal.end_date && formatExpiryDate(deal.end_date) === 'Today only!' && (
                <div class="absolute bottom-3 right-3 bg-ghibli-forest-600 text-white text-xs px-2.5 py-1 rounded-full flex items-center">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-3.5 w-3.5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  Ends today!
                </div>
              )}
            </div>
            
            <div class="p-5 flex-grow flex flex-col">
              <h3 class="font-ghibliHeading font-bold text-ghibli-forest-800 mb-2 line-clamp-2 group-hover:text-ghibli-warm-600 transition-colors">
                {deal.title}
              </h3>
              
              <div class="mt-auto pt-3 flex items-end justify-between">
                <div>
                  {deal.sale_price !== undefined && (
                    <div class="flex flex-col">
                      <span class="text-ghibli-warm-600 font-bold text-lg">
                        {formatPrice(deal.sale_price)}
                      </span>
                      {deal.original_price !== undefined && deal.original_price > deal.sale_price && (
                        <span class="text-ghibli-earth-500 text-sm line-through">
                          {formatPrice(deal.original_price)}
                        </span>
                      )}
                    </div>
                  )}
                </div>
                
                <div class="text-xs text-ghibli-earth-500 flex items-center">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  {deal.end_date ? formatExpiryDate(deal.end_date) : 'Limited time'}
                </div>
              </div>
              
              <div class="mt-3 flex items-center justify-center w-full">
                <span class="bg-gradient-to-r from-ghibli-warm-50 to-ghibli-warm-100 text-ghibli-warm-700 text-sm font-medium py-1.5 px-4 rounded-full text-center group-hover:shadow-sm transition-all w-full">
                  Get this deal
                </span>
              </div>
            </div>
          </a>
        ))}
      </div>
    ) : (
      <div class="text-center py-16">
        <div class="mb-4">
          <svg class="w-16 h-16 mx-auto text-ghibli-earth-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M20 7l-8 4m0 0L4 7m8 4v10m0-10l8-4m-8-4l-8 4m16 0l-8-4"/>
          </svg>
        </div>
        <h3 class="text-xl font-ghibliHeading font-bold text-ghibli-forest-700 mb-2">No Active Deals Right Now</h3>
        <p class="text-ghibli-earth-600 mb-6">Check back soon for exciting discounts and special offers!</p>
        <a href="/articles" class="inline-flex items-center px-5 py-2 bg-ghibli-spring-100 text-ghibli-forest-700 rounded-full font-medium hover:bg-ghibli-spring-200 transition-colors">
          Browse Articles Instead
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-1.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3" />
          </svg>
        </a>
      </div>
    )}
  </div>
</section>

<style>
  .animate-float {
    animation: float 6s ease-in-out infinite;
  }
  
  @keyframes float {
    0% {
      transform: translateY(0px);
    }
    50% {
      transform: translateY(-20px);
    }
    100% {
      transform: translateY(0px);
    }
  }
  
  .shadow-ghibli {
    box-shadow: 0 10px 15px -3px rgba(142, 155, 94, 0.08), 0 4px 6px -4px rgba(142, 155, 94, 0.05);
  }
  
  .shadow-ghibli-hover {
    box-shadow: 0 20px 25px -5px rgba(142, 155, 94, 0.15), 0 8px 10px -6px rgba(142, 155, 94, 0.1);
  }
</style>